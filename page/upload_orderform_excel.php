<!DOCTYPE html>
<html lang="ko">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>주문서 엑셀 업로드</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 3px solid #007cba;
            padding-bottom: 10px;
        }
        .upload-area {
            border: 3px dashed #007cba;
            border-radius: 10px;
            padding: 50px;
            text-align: center;
            margin-bottom: 20px;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        .upload-area:hover, .upload-area.dragover {
            background-color: #e3f2fd;
            border-color: #0056b3;
        }
        .upload-btn {
            background-color: #007cba;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin-top: 10px;
        }
        .upload-btn:hover {
            background-color: #005a8b;
        }
        .file-input {
            display: none;
        }
        .file-info {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            display: none;
        }
        .process-btn {
            background-color: #28a745;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 18px;
            width: 100%;
            margin-bottom: 20px;
        }
        .process-btn:hover {
            background-color: #218838;
        }
        .process-btn:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .result {
            margin-top: 20px;
            padding: 20px;
            border-radius: 5px;
        }
        .result.success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .result.error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info-box {
            background-color: #e9ecef;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .info-box h3 {
            margin-top: 0;
            color: #495057;
        }
        .delete-section {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 20px;
            border-radius: 5px;
            margin-top: 20px;
        }
        .delete-section h3 {
            color: #856404;
            margin-top: 0;
        }
        .delete-btn {
            background-color: #dc3545;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin-left: 10px;
        }
        .delete-btn:hover {
            background-color: #c82333;
        }
        .file-list {
            max-height: 200px;
            overflow-y: auto;
            border: 1px solid #ddd;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 10px;
        }
        .file-item {
            padding: 5px;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .file-item:last-child {
            border-bottom: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📋 주문서 엑셀 업로드</h1>
        
        <div class="info-box">
            <h3>📝 엑셀 파일 형식 안내</h3>
            <ul>
                <li><strong>B2:</strong> '상품명' 컬럼</li>
                <li><strong>C2:</strong> '옵션명' 컬럼</li>
                <li><strong>D2:</strong> '수량' 컬럼</li>
                <li><strong>파일명:</strong> '발주서_YYYYMMDDHHMMSS.xlsx' 형식</li>
            </ul>
            <p><strong>주의:</strong> 파일명에서 날짜/시간 정보를 추출하여 DB에 저장됩니다.</p>
        </div>

        <form id="uploadForm" enctype="multipart/form-data">
            <div class="upload-area" id="uploadArea">
                <p>📁 주문서 엑셀 파일을 여기에 드래그하거나 클릭하여 선택하세요</p>
                <p>지원 형식: .xlsx, .xls</p>
                <button type="button" class="upload-btn" onclick="document.getElementById('fileInput').click()">
                    파일 선택
                </button>
                <input type="file" id="fileInput" class="file-input" accept=".xlsx,.xls" />
            </div>
            
            <div class="file-info" id="fileInfo">
                <h4>선택된 파일:</h4>
                <p id="fileName"></p>
                <p id="fileSize"></p>
            </div>
            
            <button type="submit" class="process-btn" id="processBtn">
                🚀 주문서 데이터 업로드
            </button>
        </form>

        <div id="result"></div>

        <!-- 파일별 삭제 기능 -->
        <div class="delete-section">
            <h3>🗑️ 업로드된 파일별 데이터 삭제</h3>
            <p>잘못 업로드된 주문서 데이터를 파일명 단위로 삭제할 수 있습니다.</p>
            
            <div id="fileListContainer">
                <button type="button" onclick="loadFileList()" class="upload-btn">업로드된 파일 목록 새로고침</button>
                <div id="fileList" class="file-list"></div>
            </div>
        </div>
    </div>

    <script>
        const uploadArea = document.getElementById('uploadArea');
        const fileInput = document.getElementById('fileInput');
        const fileInfo = document.getElementById('fileInfo');
        const processBtn = document.getElementById('processBtn');
        const uploadForm = document.getElementById('uploadForm');

        // 드래그앤드롭 이벤트
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });

        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });

        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                handleFile(files[0]);
            }
        });

        // 파일 선택 이벤트
        fileInput.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                handleFile(e.target.files[0]);
            }
        });

        // 파일 처리 함수
        function handleFile(file) {
            const fileName = file.name;
            const fileSize = (file.size / 1024 / 1024).toFixed(2) + ' MB';
            
            document.getElementById('fileName').textContent = fileName;
            document.getElementById('fileSize').textContent = fileSize;
            fileInfo.style.display = 'block';
            
            // 파일명 검증
            if (!fileName.includes('발주서_')) {
                alert('파일명이 "발주서_" 형식이 아닙니다. 올바른 형식의 파일을 선택해주세요.');
                return;
            }
        }

        // 폼 제출
        uploadForm.addEventListener('submit', async (e) => {
            e.preventDefault();
            
            if (!fileInput.files[0]) {
                alert('파일을 선택해주세요.');
                return;
            }

            const formData = new FormData();
            formData.append('excel_file', fileInput.files[0]);

            processBtn.textContent = '처리 중...';
            processBtn.disabled = true;

            try {
                const response = await fetch('upload_orderform_excel.php', {
                    method: 'POST',
                    body: formData
                });

                const result = await response.text();
                document.getElementById('result').innerHTML = result;
                
                // 성공 시 파일 목록 새로고침
                if (result.includes('success')) {
                    setTimeout(() => {
                        loadFileList();
                    }, 1000);
                }
                
            } catch (error) {
                document.getElementById('result').innerHTML = 
                    '<div class="result error">업로드 중 오류가 발생했습니다: ' + error.message + '</div>';
            } finally {
                processBtn.textContent = '🚀 주문서 데이터 업로드';
                processBtn.disabled = false;
            }
        });

        // 파일 목록 로드
        async function loadFileList() {
            try {
                const response = await fetch('upload_orderform_excel.php?action=list_files');
                const result = await response.json();
                
                const fileListDiv = document.getElementById('fileList');
                if (result.files && result.files.length > 0) {
                    fileListDiv.innerHTML = result.files.map(file => 
                        `<div class="file-item">
                            <span>${file.filename} (${file.count}개 항목, ${file.date_join})</span>
                            <button class="delete-btn" onclick="deleteFileData('${file.filename}')">삭제</button>
                        </div>`
                    ).join('');
                } else {
                    fileListDiv.innerHTML = '<p>업로드된 파일이 없습니다.</p>';
                }
            } catch (error) {
                console.error('파일 목록 로드 실패:', error);
            }
        }

        // 파일별 데이터 삭제
        async function deleteFileData(filename) {
            if (!confirm(`"${filename}" 파일의 모든 데이터를 삭제하시겠습니까?`)) {
                return;
            }

            try {
                const response = await fetch('upload_orderform_excel.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: `action=delete_file&filename=${encodeURIComponent(filename)}`
                });

                const result = await response.text();
                document.getElementById('result').innerHTML = result;
                
                // 삭제 후 파일 목록 새로고침
                setTimeout(() => {
                    loadFileList();
                }, 1000);
                
            } catch (error) {
                alert('삭제 중 오류가 발생했습니다: ' + error.message);
            }
        }

        // 페이지 로드 시 파일 목록 로드
        window.addEventListener('load', () => {
            loadFileList();
        });
    </script>
</body>
</html>

<?php
// PHP 처리 부분
use PhpOffice\PhpSpreadsheet\IOFactory;

// 파일 목록 조회 요청 처리
if (isset($_GET['action']) && $_GET['action'] === 'list_files') {
    require_once '../conf/Database.php';

    try {
        $db = new Database();

        // 업로드된 파일별 통계 조회
        $sql = "SELECT
                    SUBSTRING_INDEX(SUBSTRING_INDEX(filename, '/', -1), '.', 1) as filename,
                    COUNT(*) as count,
                    DATE_FORMAT(date_join, '%Y-%m-%d %H:%i:%s') as date_join
                FROM orderForm
                GROUP BY filename, date_join
                ORDER BY date_join DESC";

        $files = $db->select($sql);

        header('Content-Type: application/json');
        echo json_encode(['files' => $files]);
        exit;

    } catch (Exception $e) {
        header('Content-Type: application/json');
        echo json_encode(['error' => $e->getMessage()]);
        exit;
    }
}

// 파일별 데이터 삭제 요청 처리
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'delete_file') {
    require_once '../conf/Database.php';

    try {
        $db = new Database();
        $filename = $_POST['filename'];

        // 파일명으로 데이터 삭제
        $deletedRows = $db->delete('orderForm', 'filename LIKE :filename', ['filename' => "%{$filename}%"]);

        echo '<div class="result success">';
        echo '<h3>🗑️ 파일 데이터 삭제 완료</h3>';
        echo "<p>파일명: {$filename}</p>";
        echo "<p>삭제된 항목 수: {$deletedRows}개</p>";
        echo '</div>';
        exit;

    } catch (Exception $e) {
        echo '<div class="result error">';
        echo '<h3>❌ 삭제 실패</h3>';
        echo '<p>' . htmlspecialchars($e->getMessage()) . '</p>';
        echo '</div>';
        exit;
    }
}

// 엑셀 파일 업로드 처리
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['excel_file'])) {
    require_once '../vendor/autoload.php';
    require_once '../conf/Database.php';

    try {
        // 데이터베이스 연결
        $db = new Database();

        // orderForm 테이블 생성 (존재하지 않는 경우)
        $createTableSql = "
            CREATE TABLE IF NOT EXISTS orderForm (
                id INT AUTO_INCREMENT PRIMARY KEY,
                product_name_sur VARCHAR(255) NOT NULL,
                product_name_option VARCHAR(255) NOT NULL,
                order_amount INT NOT NULL,
                date_join DATETIME NOT NULL,
                filename VARCHAR(255) NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ";

        $db->connect()->exec($createTableSql);

        // 업로드된 파일 처리
        $uploadedFile = $_FILES['excel_file']['tmp_name'];
        $originalFileName = $_FILES['excel_file']['name'];

        if (!file_exists($uploadedFile)) {
            throw new Exception('파일 업로드에 실패했습니다.');
        }

        // 파일명에서 날짜/시간 추출
        $dateJoin = null;
        if (preg_match('/발주서_(\d{4})(\d{2})(\d{2})(\d{2})(\d{2})(\d{2})/', $originalFileName, $matches)) {
            $year = $matches[1];
            $month = $matches[2];
            $day = $matches[3];
            $hour = $matches[4];
            $minute = $matches[5];
            $second = $matches[6];
            $dateJoin = "{$year}-{$month}-{$day} {$hour}:{$minute}:{$second}";
        } else {
            throw new Exception('파일명 형식이 올바르지 않습니다. "발주서_YYYYMMDDHHMMSS" 형식이어야 합니다.');
        }

        // 텍스트 치환 함수
        function replaceSpecialChars($text) {
            $text = str_replace('🕔', '이', $text);
            $text = str_replace('잠❹', '잠옷', $text);
            $text = str_replace('로❹', '로우', $text);
            return $text;
        }

        // 엑셀 파일 읽기
        $spreadsheet = IOFactory::load($uploadedFile);
        $worksheet = $spreadsheet->getActiveSheet();
        $highestRow = $worksheet->getHighestRow();

        echo '<div class="result success">';
        echo '<h3>📊 주문서 엑셀 파일 처리 결과</h3>';
        echo '<p>파일명: ' . htmlspecialchars($originalFileName) . '</p>';
        echo '<p>추출된 날짜: ' . $dateJoin . '</p>';
        echo '<p>총 ' . ($highestRow - 2) . '개의 데이터를 처리합니다.</p>';

        $successCount = 0;
        $errorCount = 0;
        $errors = [];

        // 트랜잭션 시작
        $db->beginTransaction();

        // 3행부터 데이터 처리 (1행은 빈 행, 2행은 헤더)
        for ($row = 3; $row <= $highestRow; $row++) {
            try {
                $productNameSur = $worksheet->getCell('B' . $row)->getValue();
                $productNameOption = $worksheet->getCell('C' . $row)->getValue();
                $orderAmount = $worksheet->getCell('D' . $row)->getValue();

                // 빈 행 건너뛰기
                if (empty($productNameSur) && empty($productNameOption) && empty($orderAmount)) {
                    continue;
                }

                // 특수 문자 치환
                $productNameSur = replaceSpecialChars($productNameSur);
                $productNameOption = replaceSpecialChars($productNameOption);

                // 수량이 숫자가 아닌 경우 0으로 처리
                $orderAmount = is_numeric($orderAmount) ? intval($orderAmount) : 0;

                // 데이터 삽입
                $data = [
                    'product_name_sur' => $productNameSur,
                    'product_name_option' => $productNameOption,
                    'order_amount' => $orderAmount,
                    'date_join' => $dateJoin,
                    'filename' => $originalFileName
                ];

                $insertId = $db->insert('orderForm', $data);
                $successCount++;

                echo '<p>✅ 행 ' . $row . ': ' . htmlspecialchars($productNameSur) . ' - ' . htmlspecialchars($productNameOption) . ' (수량: ' . $orderAmount . ')</p>';

            } catch (Exception $e) {
                $errorCount++;
                $errors[] = "행 {$row}: " . $e->getMessage();
                echo '<p>❌ 행 ' . $row . ': 오류 - ' . htmlspecialchars($e->getMessage()) . '</p>';
            }
        }

        // 트랜잭션 커밋
        $db->commit();

        echo '<hr>';
        echo '<h4>📈 처리 완료</h4>';
        echo '<p>성공: ' . $successCount . '개</p>';
        echo '<p>실패: ' . $errorCount . '개</p>';

        if (!empty($errors)) {
            echo '<h4>❌ 오류 목록</h4>';
            foreach ($errors as $error) {
                echo '<p style="color: red;">' . htmlspecialchars($error) . '</p>';
            }
        }

        echo '</div>';

    } catch (Exception $e) {
        // 트랜잭션 롤백
        if (isset($db)) {
            $db->rollback();
        }

        echo '<div class="result error">';
        echo '<h3>❌ 처리 실패</h3>';
        echo '<p>' . htmlspecialchars($e->getMessage()) . '</p>';
        echo '</div>';
    }
}
?>
