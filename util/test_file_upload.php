<!DOCTYPE html>
<html lang="ko">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>파일 업로드 테스트</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .upload-area {
            border: 3px dashed #007cba;
            border-radius: 10px;
            padding: 50px;
            text-align: center;
            margin-bottom: 20px;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        .upload-area:hover {
            background-color: #e3f2fd;
            border-color: #0056b3;
        }
        .file-info {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .btn {
            padding: 10px 20px;
            background-color: #007cba;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background-color: #005a8b;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📁 파일 업로드 테스트</h1>
        
        <p>이 페이지는 호스팅 서버에서 파일 업로드가 제대로 작동하는지 테스트합니다.</p>
        
        <div class="upload-area" id="uploadArea">
            <p>📁 엑셀 파일을 여기에 드래그하거나 클릭하여 선택하세요</p>
            <p>지원 형식: .xlsx, .xls</p>
            <button type="button" class="btn" onclick="document.getElementById('fileInput').click()">
                파일 선택
            </button>
            <input type="file" id="fileInput" style="display: none;" accept=".xlsx,.xls,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-excel" />
        </div>
        
        <div id="fileInfo" class="file-info" style="display: none;">
            <h3>선택된 파일 정보:</h3>
            <div id="fileDetails"></div>
        </div>
        
        <button type="button" class="btn" onclick="clearFile()">파일 지우기</button>
        
        <div id="result" class="result" style="display: none;"></div>
    </div>

    <script>
        const uploadArea = document.getElementById('uploadArea');
        const fileInput = document.getElementById('fileInput');
        const fileInfo = document.getElementById('fileInfo');
        const fileDetails = document.getElementById('fileDetails');
        const result = document.getElementById('result');

        // 드래그앤드롭 이벤트
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.style.backgroundColor = '#e3f2fd';
        });

        uploadArea.addEventListener('dragleave', () => {
            uploadArea.style.backgroundColor = '';
        });

        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.style.backgroundColor = '';
            
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                handleFile(files[0]);
            }
        });

        // 파일 선택 이벤트
        fileInput.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                handleFile(e.target.files[0]);
            }
        });

        // 파일 처리 함수
        function handleFile(file) {
            console.log('파일 선택됨:', file);
            
            // 파일 정보 표시
            const fileInfoHtml = `
                <p><strong>파일명:</strong> ${file.name}</p>
                <p><strong>파일 크기:</strong> ${(file.size / 1024 / 1024).toFixed(2)} MB</p>
                <p><strong>MIME 타입:</strong> ${file.type}</p>
                <p><strong>마지막 수정:</strong> ${new Date(file.lastModified).toLocaleString()}</p>
            `;
            
            fileDetails.innerHTML = fileInfoHtml;
            fileInfo.style.display = 'block';
            
            // 파일 검증
            validateFile(file);
        }
        
        function validateFile(file) {
            const allowedTypes = [
                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 
                'application/vnd.ms-excel',
                'application/excel',
                'application/x-excel',
                'application/x-msexcel'
            ];
            
            const fileName = file.name.toLowerCase();
            const allowedExtensions = ['.xlsx', '.xls'];
            const hasValidExtension = allowedExtensions.some(ext => fileName.endsWith(ext));
            
            let validationResult = {
                mimeTypeValid: allowedTypes.includes(file.type),
                extensionValid: hasValidExtension,
                overall: allowedTypes.includes(file.type) || hasValidExtension
            };
            
            // 검증 결과 표시
            const resultHtml = `
                <h3>🔍 파일 검증 결과</h3>
                <p><strong>MIME 타입 검증:</strong> ${validationResult.mimeTypeValid ? '✅ 통과' : '❌ 실패'}</p>
                <p><strong>확장자 검증:</strong> ${validationResult.extensionValid ? '✅ 통과' : '❌ 실패'}</p>
                <p><strong>전체 검증:</strong> ${validationResult.overall ? '✅ 통과' : '❌ 실패'}</p>
                
                <h4>📋 상세 정보</h4>
                <pre>${JSON.stringify({
                    fileName: file.name,
                    fileType: file.type,
                    fileSize: file.size,
                    allowedTypes: allowedTypes,
                    allowedExtensions: allowedExtensions,
                    validation: validationResult
                }, null, 2)}</pre>
                
                <h4>🌐 브라우저 정보</h4>
                <pre>${JSON.stringify({
                    userAgent: navigator.userAgent,
                    platform: navigator.platform,
                    language: navigator.language,
                    cookieEnabled: navigator.cookieEnabled
                }, null, 2)}</pre>
            `;
            
            result.innerHTML = resultHtml;
            result.style.display = 'block';
            
            // 콘솔에도 출력
            console.log('파일 검증 결과:', validationResult);
            console.log('파일 정보:', {
                name: file.name,
                type: file.type,
                size: file.size
            });
        }
        
        function clearFile() {
            fileInput.value = '';
            fileInfo.style.display = 'none';
            result.style.display = 'none';
        }
        
        // 페이지 로드 시 환경 정보 표시
        window.addEventListener('load', () => {
            console.log('페이지 로드 완료');
            console.log('브라우저 정보:', {
                userAgent: navigator.userAgent,
                platform: navigator.platform
            });
        });
    </script>
</body>
</html>
