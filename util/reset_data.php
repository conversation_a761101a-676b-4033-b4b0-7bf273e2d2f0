<!DOCTYPE html>
<html lang="ko">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>데이터 리셋 - 재고관리시스템</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #dc3545;
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 3px solid #dc3545;
            padding-bottom: 10px;
        }
        .warning-box {
            background-color: #f8d7da;
            border: 2px solid #dc3545;
            color: #721c24;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
        }
        .warning-box h2 {
            color: #721c24;
            margin-top: 0;
            font-size: 24px;
        }
        .warning-box ul {
            margin: 15px 0;
            padding-left: 20px;
        }
        .warning-box li {
            margin: 8px 0;
            font-weight: bold;
        }
        .info-box {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .btn {
            padding: 15px 30px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            text-decoration: none;
            display: inline-block;
            margin: 10px 5px;
            text-align: center;
        }
        .btn-danger {
            background-color: #dc3545;
            color: white;
        }
        .btn-danger:hover {
            background-color: #c82333;
        }
        .btn-secondary {
            background-color: #6c757d;
            color: white;
        }
        .btn-secondary:hover {
            background-color: #545b62;
        }
        .btn-success {
            background-color: #28a745;
            color: white;
        }
        .btn-success:hover {
            background-color: #218838;
        }
        .actions {
            text-align: center;
            margin-top: 30px;
        }
        .result {
            margin-top: 20px;
            padding: 20px;
            border-radius: 5px;
        }
        .result.success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .result.error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .table-stats {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .table-stats h3 {
            margin-top: 0;
            color: #495057;
        }
        .stat-item {
            display: flex;
            justify-content: space-between;
            padding: 5px 0;
            border-bottom: 1px solid #dee2e6;
        }
        .stat-item:last-child {
            border-bottom: none;
        }
        .confirmation-step {
            display: none;
            background-color: #fff3cd;
            border: 2px solid #ffc107;
            padding: 20px;
            border-radius: 10px;
            margin-top: 20px;
        }
        .confirmation-step.show {
            display: block;
        }
        .final-warning {
            background-color: #f8d7da;
            border: 2px solid #dc3545;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
            text-align: center;
        }
        .checkbox-container {
            margin: 20px 0;
            text-align: center;
        }
        .checkbox-container input[type="checkbox"] {
            transform: scale(1.5);
            margin-right: 10px;
        }
        .checkbox-container label {
            font-weight: bold;
            color: #721c24;
        }
    </style>
</head>
<body>
    <?php include '../components/navbar.php'; ?>

    <div class="container page-content">
        <h1>🗑️ 데이터베이스 초기화</h1>
        
        <div class="warning-box">
            <h2>⚠️ 위험한 작업입니다!</h2>
            <p><strong>이 작업은 다음 테이블의 모든 데이터를 영구적으로 삭제합니다:</strong></p>
            <ul>
                <li>📋 orderForm - 모든 주문 데이터</li>
                <li>🔗 product_mapping - 모든 상품 매핑 데이터</li>
                <li>📦 product_master - 모든 상품 마스터 데이터</li>
            </ul>
            <p style="font-size: 18px; font-weight: bold; color: #dc3545;">
                ❌ 삭제된 데이터는 복구할 수 없습니다!
            </p>
        </div>

<?php
require_once '../conf/Database.php';

// 메시지 표시 함수
function showMessage($message, $type = 'info') {
    echo "<div class='result {$type}'>{$message}</div>";
}

try {
    $db = new Database();
    
    // POST 요청 처리 (실제 삭제)
    if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['confirm_delete'])) {
        if ($_POST['confirm_delete'] === 'yes' && isset($_POST['final_confirmation'])) {
            
            echo '<div class="result success">';
            echo '<h3>🗑️ 데이터 삭제 진행 중...</h3>';
            
            $deletedCounts = [];
            $errors = [];
            
            // 트랜잭션 시작
            $db->beginTransaction();
            
            try {
                // 1. orderForm 테이블 데이터 삭제
                $orderFormCount = $db->selectOne("SELECT COUNT(*) as count FROM orderForm")['count'];
                $db->connect()->exec("DELETE FROM orderForm");
                $deletedCounts['orderForm'] = $orderFormCount;
                echo "<p>✅ orderForm 테이블: {$orderFormCount}개 데이터 삭제 완료</p>";
                
                // 2. product_mapping 테이블 데이터 삭제
                $mappingCount = $db->selectOne("SELECT COUNT(*) as count FROM product_mapping")['count'];
                $db->connect()->exec("DELETE FROM product_mapping");
                $deletedCounts['product_mapping'] = $mappingCount;
                echo "<p>✅ product_mapping 테이블: {$mappingCount}개 데이터 삭제 완료</p>";
                
                // 3. product_master 테이블 데이터 삭제
                $masterCount = $db->selectOne("SELECT COUNT(*) as count FROM product_master")['count'];
                $db->connect()->exec("DELETE FROM product_master");
                $deletedCounts['product_master'] = $masterCount;
                echo "<p>✅ product_master 테이블: {$masterCount}개 데이터 삭제 완료</p>";
                
                // AUTO_INCREMENT 값 초기화
                $db->connect()->exec("ALTER TABLE orderForm AUTO_INCREMENT = 1");
                $db->connect()->exec("ALTER TABLE product_mapping AUTO_INCREMENT = 1");
                echo "<p>✅ AUTO_INCREMENT 값 초기화 완료</p>";
                
                // 트랜잭션 커밋
                $db->commit();
                
                $totalDeleted = array_sum($deletedCounts);
                echo '<hr>';
                echo '<h4>🎉 데이터베이스 초기화 완료!</h4>';
                echo "<p><strong>총 {$totalDeleted}개의 데이터가 삭제되었습니다.</strong></p>";
                echo '<p>이제 새로운 데이터를 업로드할 수 있습니다.</p>';
                
                echo '<div style="text-align: center; margin-top: 20px;">';
                echo '<a href="../page/view_product_master.php" class="btn btn-success">📦 상품 마스터 페이지로 이동</a>';
                echo '<a href="../page/upload_orderform_excel.php" class="btn btn-success">📤 주문서 업로드 페이지로 이동</a>';
                echo '</div>';
                
                echo '</div>';
                
            } catch (Exception $e) {
                // 트랜잭션 롤백
                $db->rollback();
                throw $e;
            }
            
        } else {
            showMessage('❌ 최종 확인이 완료되지 않았습니다.', 'error');
        }
    } else {
        // 현재 데이터 상태 표시
        echo '<div class="info-box">';
        echo '<h3>📊 현재 데이터베이스 상태</h3>';
        echo '<p>삭제하기 전에 현재 저장된 데이터를 확인하세요.</p>';
        echo '</div>';
        
        echo '<div class="table-stats">';
        echo '<h3>📈 테이블별 데이터 현황</h3>';
        
        // 각 테이블의 데이터 수 조회
        $orderFormCount = 0;
        $mappingCount = 0;
        $masterCount = 0;
        
        try {
            $orderFormCount = $db->selectOne("SELECT COUNT(*) as count FROM orderForm")['count'];
        } catch (Exception $e) {
            $orderFormCount = "오류: " . $e->getMessage();
        }
        
        try {
            $mappingCount = $db->selectOne("SELECT COUNT(*) as count FROM product_mapping")['count'];
        } catch (Exception $e) {
            $mappingCount = "오류: " . $e->getMessage();
        }
        
        try {
            $masterCount = $db->selectOne("SELECT COUNT(*) as count FROM product_master")['count'];
        } catch (Exception $e) {
            $masterCount = "오류: " . $e->getMessage();
        }
        
        echo '<div class="stat-item">';
        echo '<span><strong>📋 orderForm (주문 데이터)</strong></span>';
        echo '<span>' . (is_numeric($orderFormCount) ? number_format($orderFormCount) . '개' : $orderFormCount) . '</span>';
        echo '</div>';
        
        echo '<div class="stat-item">';
        echo '<span><strong>🔗 product_mapping (상품 매핑)</strong></span>';
        echo '<span>' . (is_numeric($mappingCount) ? number_format($mappingCount) . '개' : $mappingCount) . '</span>';
        echo '</div>';
        
        echo '<div class="stat-item">';
        echo '<span><strong>📦 product_master (상품 마스터)</strong></span>';
        echo '<span>' . (is_numeric($masterCount) ? number_format($masterCount) . '개' : $masterCount) . '</span>';
        echo '</div>';
        
        $totalCount = 0;
        if (is_numeric($orderFormCount) && is_numeric($mappingCount) && is_numeric($masterCount)) {
            $totalCount = $orderFormCount + $mappingCount + $masterCount;
            echo '<div class="stat-item" style="border-top: 2px solid #007cba; font-weight: bold; color: #007cba;">';
            echo '<span>📊 총 데이터 수</span>';
            echo '<span>' . number_format($totalCount) . '개</span>';
            echo '</div>';
        }
        
        echo '</div>';
        
        if ($totalCount > 0) {
            echo '<div class="actions">';
            echo '<button type="button" class="btn btn-danger" onclick="showConfirmation()">🗑️ 모든 데이터 삭제</button>';
            echo '<a href="../page/view_product_master.php" class="btn btn-secondary">📦 상품 마스터 보기</a>';
            echo '</div>';
        } else {
            echo '<div class="info-box">';
            echo '<h3>ℹ️ 삭제할 데이터가 없습니다</h3>';
            echo '<p>현재 데이터베이스에 저장된 데이터가 없습니다.</p>';
            echo '<div style="text-align: center; margin-top: 15px;">';
            echo '<a href="../page/upload_orderform_excel.php" class="btn btn-success">📤 주문서 업로드하기</a>';
            echo '<a href="../util/init_excel_product_master.php" class="btn btn-success">📦 상품 마스터 업로드하기</a>';
            echo '</div>';
            echo '</div>';
        }
    }
    
} catch (Exception $e) {
    showMessage('❌ 오류 발생: ' . $e->getMessage(), 'error');
}
?>

        <!-- 확인 단계 -->
        <div id="confirmationStep" class="confirmation-step">
            <h3>⚠️ 최종 확인</h3>
            <div class="final-warning">
                <h4>정말로 모든 데이터를 삭제하시겠습니까?</h4>
                <p>이 작업은 되돌릴 수 없습니다!</p>
            </div>
            
            <form method="POST" id="deleteForm">
                <div class="checkbox-container">
                    <input type="checkbox" id="finalConfirm" name="final_confirmation" required>
                    <label for="finalConfirm">네, 모든 데이터를 영구적으로 삭제하는 것에 동의합니다.</label>
                </div>
                
                <input type="hidden" name="confirm_delete" value="yes">
                
                <div class="actions">
                    <button type="submit" class="btn btn-danger" id="finalDeleteBtn" disabled>
                        🗑️ 최종 삭제 실행
                    </button>
                    <button type="button" class="btn btn-secondary" onclick="hideConfirmation()">
                        ❌ 취소
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script>
        function showConfirmation() {
            document.getElementById('confirmationStep').classList.add('show');
            document.getElementById('finalConfirm').checked = false;
            document.getElementById('finalDeleteBtn').disabled = true;
        }
        
        function hideConfirmation() {
            document.getElementById('confirmationStep').classList.remove('show');
        }
        
        // 체크박스 상태에 따라 버튼 활성화/비활성화
        document.getElementById('finalConfirm').addEventListener('change', function() {
            document.getElementById('finalDeleteBtn').disabled = !this.checked;
        });
        
        // 폼 제출 시 한 번 더 확인
        document.getElementById('deleteForm').addEventListener('submit', function(e) {
            if (!confirm('정말로 모든 데이터를 삭제하시겠습니까?\n\n이 작업은 되돌릴 수 없습니다!')) {
                e.preventDefault();
            }
        });
    </script>
</body>
</html>
