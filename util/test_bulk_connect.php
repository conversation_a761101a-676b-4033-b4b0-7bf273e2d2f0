<!DOCTYPE html>
<html lang="ko">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>일괄 연결 테스트 - 재고관리시스템</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 15px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .form-control {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        .btn {
            padding: 10px 20px;
            background-color: #007cba;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background-color: #005a8b;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 일괄 연결 테스트</h1>
        
        <p>이 페이지는 일괄 연결 AJAX 요청을 직접 테스트합니다.</p>
        
        <form id="testForm">
            <div class="form-group">
                <label for="product_name_sur">상품명:</label>
                <input type="text" id="product_name_sur" class="form-control" value="테스트상품" required>
            </div>
            
            <div class="form-group">
                <label for="product_name_option">옵션명:</label>
                <input type="text" id="product_name_option" class="form-control" value="테스트옵션" required>
            </div>
            
            <div class="form-group">
                <label for="option_no">옵션번호:</label>
                <input type="text" id="option_no" class="form-control" placeholder="연결할 옵션번호 입력" required>
            </div>
            
            <button type="button" class="btn" onclick="testBulkConnect()">일괄 연결 테스트</button>
            <button type="button" class="btn" onclick="clearResult()">결과 지우기</button>
        </form>
        
        <div id="result" class="result" style="display: none;">
            <h3>테스트 결과:</h3>
            <div id="resultContent"></div>
        </div>
    </div>

    <script>
        async function testBulkConnect() {
            const productNameSur = document.getElementById('product_name_sur').value;
            const productNameOption = document.getElementById('product_name_option').value;
            const optionNo = document.getElementById('option_no').value;
            
            if (!productNameSur || !productNameOption || !optionNo) {
                alert('모든 필드를 입력해주세요.');
                return;
            }
            
            const resultDiv = document.getElementById('result');
            const resultContent = document.getElementById('resultContent');
            
            resultDiv.style.display = 'block';
            resultContent.innerHTML = '<p>요청 중...</p>';
            
            try {
                const formData = new FormData();
                formData.append('action', 'bulk_create_mapping');
                formData.append('product_name_sur', productNameSur);
                formData.append('product_name_option', productNameOption);
                formData.append('option_no', optionNo);
                
                console.log('전송 데이터:', {
                    action: 'bulk_create_mapping',
                    product_name_sur: productNameSur,
                    product_name_option: productNameOption,
                    option_no: optionNo
                });
                
                const response = await fetch('../page/compare_product_stock.php', {
                    method: 'POST',
                    body: formData
                });
                
                console.log('응답 상태:', response.status);
                console.log('응답 헤더:', response.headers);
                
                const result = await response.text();
                console.log('서버 응답:', result);
                
                let resultHTML = `
                    <h4>📊 요청 정보</h4>
                    <pre>${JSON.stringify({
                        product_name_sur: productNameSur,
                        product_name_option: productNameOption,
                        option_no: optionNo
                    }, null, 2)}</pre>
                    
                    <h4>📡 응답 정보</h4>
                    <p><strong>상태 코드:</strong> ${response.status}</p>
                    <p><strong>Content-Type:</strong> ${response.headers.get('content-type')}</p>
                    
                    <h4>📝 서버 응답</h4>
                    <pre>${result}</pre>
                    
                    <h4>🔍 분석</h4>
                `;
                
                if (result.includes('✅') || result.includes('매핑 성공')) {
                    resultHTML += '<p style="color: green; font-weight: bold;">✅ 성공으로 판단됨</p>';
                } else if (result.includes('❌')) {
                    resultHTML += '<p style="color: red; font-weight: bold;">❌ 실패로 판단됨</p>';
                } else if (result.includes('⚠️')) {
                    resultHTML += '<p style="color: orange; font-weight: bold;">⚠️ 경고로 판단됨</p>';
                } else {
                    resultHTML += '<p style="color: gray; font-weight: bold;">❓ 알 수 없는 응답</p>';
                }
                
                resultContent.innerHTML = resultHTML;
                
            } catch (error) {
                console.error('요청 실패:', error);
                resultContent.innerHTML = `
                    <h4>❌ 요청 실패</h4>
                    <p><strong>오류:</strong> ${error.message}</p>
                    <pre>${error.stack}</pre>
                `;
            }
        }
        
        function clearResult() {
            document.getElementById('result').style.display = 'none';
        }
        
        // 페이지 로드 시 상품 마스터에서 샘플 데이터 가져오기
        window.addEventListener('load', async () => {
            try {
                const response = await fetch('../page/view_product_master.php');
                console.log('상품 마스터 페이지 로드 완료');
            } catch (error) {
                console.log('상품 마스터 페이지 로드 실패:', error);
            }
        });
    </script>
</body>
</html>
