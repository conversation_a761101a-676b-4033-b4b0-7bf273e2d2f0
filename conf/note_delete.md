
#mysql -u root -p

# Brew/mysql 정보
@ Root
Pw : 12345 
@ aliensoft001
alien001!!


DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=mysql
DB_USERNAME=root
DB_PASSWORD=12345

# DB 명령어
SHOW DATABASES; // db 리스트 보기
USE DB명;// DB 선택 
select user,host from user; // 
-- 사용자 생성 (이미 존재하면 에러 무시)
CREATE USER IF NOT EXISTS 'aliensoft001'@'localhost' IDENTIFIED BY 'alien1004!!';

-- 데이터베이스 생성 (이미 존재하면 에러 무시)
CREATE DATABASE IF NOT EXISTS aliensoft001;
