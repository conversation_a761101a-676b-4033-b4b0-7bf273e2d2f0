<!DOCTYPE html>
<html lang="ko">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>데이터베이스 연결 테스트</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 데이터베이스 연결 테스트</h1>
        
        <?php
        echo '<h2>1. 설정 파일 확인</h2>';
        
        // db_info.php 파일 확인
        $dbInfoPath = '../conf/db_info.php';
        if (file_exists($dbInfoPath)) {
            echo '<div class="success">✅ db_info.php 파일 존재</div>';
            
            require_once $dbInfoPath;
            
            echo '<div class="info">';
            echo '<h3>데이터베이스 설정:</h3>';
            echo '<pre>';
            echo "서버: {$server}\n";
            echo "데이터베이스: {$db}\n";
            echo "포트: {$port}\n";
            echo "문자셋: {$charset}\n";
            echo "사용자명: {$username}\n";
            echo "비밀번호: " . str_repeat('*', strlen($password)) . "\n";
            echo '</pre>';
            echo '</div>';
        } else {
            echo '<div class="error">❌ db_info.php 파일이 없습니다</div>';
            exit;
        }
        
        echo '<h2>2. 직접 PDO 연결 테스트</h2>';
        
        try {
            // 기본 연결 시도
            $dsn = "mysql:host={$server};port={$port};dbname={$db}";
            echo '<div class="info">DSN: ' . $dsn . '</div>';
            
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
            ];
            
            $pdo = new PDO($dsn, $username, $password, $options);
            echo '<div class="success">✅ PDO 연결 성공</div>';
            
            // 문자셋 설정 테스트
            echo '<h3>문자셋 설정 테스트:</h3>';
            
            try {
                $pdo->exec("SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci");
                echo '<div class="success">✅ utf8mb4 설정 성공</div>';
            } catch (PDOException $e) {
                echo '<div class="error">❌ utf8mb4 설정 실패: ' . $e->getMessage() . '</div>';
                
                try {
                    $pdo->exec("SET NAMES utf8 COLLATE utf8_unicode_ci");
                    echo '<div class="success">✅ utf8 설정 성공</div>';
                } catch (PDOException $e2) {
                    echo '<div class="error">❌ utf8 설정 실패: ' . $e2->getMessage() . '</div>';
                    
                    try {
                        $pdo->exec("SET NAMES utf8");
                        echo '<div class="success">✅ 기본 utf8 설정 성공</div>';
                    } catch (PDOException $e3) {
                        echo '<div class="error">❌ 기본 utf8 설정 실패: ' . $e3->getMessage() . '</div>';
                    }
                }
            }
            
            // 현재 문자셋 확인
            $stmt = $pdo->query("SHOW VARIABLES LIKE 'character_set%'");
            $charsets = $stmt->fetchAll();
            
            echo '<h3>현재 MySQL 문자셋 설정:</h3>';
            echo '<div class="info">';
            echo '<pre>';
            foreach ($charsets as $charset) {
                echo $charset['Variable_name'] . ': ' . $charset['Value'] . "\n";
            }
            echo '</pre>';
            echo '</div>';
            
            // MySQL 버전 확인
            $version = $pdo->query("SELECT VERSION()")->fetchColumn();
            echo '<div class="info">MySQL 버전: ' . $version . '</div>';
            
        } catch (PDOException $e) {
            echo '<div class="error">❌ PDO 연결 실패: ' . $e->getMessage() . '</div>';
        }
        
        echo '<h2>3. Database 클래스 테스트</h2>';
        
        try {
            require_once '../conf/Database.php';
            $db = new Database();
            
            $connection = $db->connect();
            if ($connection) {
                echo '<div class="success">✅ Database 클래스 연결 성공</div>';
                
                // 간단한 쿼리 테스트
                $result = $db->selectOne("SELECT 1 as test");
                if ($result && $result['test'] == 1) {
                    echo '<div class="success">✅ 쿼리 실행 성공</div>';
                } else {
                    echo '<div class="error">❌ 쿼리 실행 실패</div>';
                }
                
                // 테이블 존재 확인
                $tables = $db->select("SHOW TABLES");
                echo '<div class="info">';
                echo '<h3>데이터베이스 테이블 목록:</h3>';
                echo '<pre>';
                foreach ($tables as $table) {
                    echo implode(', ', $table) . "\n";
                }
                echo '</pre>';
                echo '</div>';
                
            } else {
                echo '<div class="error">❌ Database 클래스 연결 실패</div>';
            }
            
        } catch (Exception $e) {
            echo '<div class="error">❌ Database 클래스 오류: ' . $e->getMessage() . '</div>';
        }
        
        echo '<h2>4. PHP 환경 정보</h2>';
        echo '<div class="info">';
        echo '<pre>';
        echo "PHP 버전: " . PHP_VERSION . "\n";
        echo "PDO 확장: " . (extension_loaded('pdo') ? '설치됨' : '설치되지 않음') . "\n";
        echo "PDO MySQL 드라이버: " . (extension_loaded('pdo_mysql') ? '설치됨' : '설치되지 않음') . "\n";
        echo "현재 시간: " . date('Y-m-d H:i:s') . "\n";
        echo '</pre>';
        echo '</div>';
        ?>
        
        <div style="text-align: center; margin-top: 30px;">
            <a href="../page/view_product_master.php" style="padding: 10px 20px; background-color: #007cba; color: white; text-decoration: none; border-radius: 5px;">메인 페이지로 돌아가기</a>
        </div>
    </div>
</body>
</html>
