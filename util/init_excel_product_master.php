<!DOCTYPE html>
<html lang="ko">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>상품 마스터 등록 - 재고관리시스템</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .upload-area {
            border: 3px dashed #007cba;
            border-radius: 10px;
            padding: 50px;
            text-align: center;
            background-color: #f8f9fa;
            margin-bottom: 20px;
            transition: all 0.3s ease;
        }
        .upload-area.dragover {
            border-color: #28a745;
            background-color: #e8f5e9;
        }
        .upload-area p {
            margin: 10px 0;
            color: #666;
        }
        .file-input {
            display: none;
        }
        .upload-btn {
            background: #007cba;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
        }
        .upload-btn:hover {
            background: #005a8b;
        }
        .process-btn {
            background: #28a745;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            width: 100%;
            margin-top: 20px;
            display: none;
        }
        .process-btn:hover {
            background: #1e7e34;
        }
        .file-info {
            background: #e9ecef;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            display: none;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .excel-format {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .excel-format h3 {
            color: #856404;
            margin-top: 0;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
    </style>
</head>
<body>
    <?php include '../components/navbar.php'; ?>

    <div class="container page-content">
        <h1>📊 상품 마스터 등록</h1>
        
        <div class="excel-format">
            <h3>📋 엑셀 파일 형식</h3>
            <p>엑셀 파일은 다음 형식이어야 합니다:</p>
            <table>
                <tr>
                    <th>A1: No</th>
                    <th>B1: 상품명</th>
                    <th>C1: 잔량</th>
                </tr>
                <tr>
                    <td>1</td>
                    <td>상품명 예시</td>
                    <td>100</td>
                </tr>
                <tr>
                    <td>2</td>
                    <td>다른 상품</td>
                    <td>50</td>
                </tr>
            </table>
            <p><small>* 첫 번째 행은 헤더이고, 두 번째 행부터 실제 데이터가 시작됩니다.</small></p>
        </div>

        <form id="uploadForm" enctype="multipart/form-data">
            <div class="upload-area" id="uploadArea">
                <p>📁 엑셀 파일을 여기에 드래그하거나 클릭하여 선택하세요</p>
                <p>지원 형식: .xlsx, .xls</p>
                <button type="button" class="upload-btn" onclick="document.getElementById('fileInput').click()">
                    파일 선택
                </button>
                <input type="file" id="fileInput" class="file-input" accept=".xlsx,.xls,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-excel" />
            </div>
            
            <div class="file-info" id="fileInfo">
                <h4>선택된 파일:</h4>
                <p id="fileName"></p>
                <p id="fileSize"></p>
            </div>
            
            <button type="submit" class="process-btn" id="processBtn">
                🚀 데이터베이스에 업로드
            </button>
        </form>

        <div id="result"></div>
    </div>

    <script>
        const uploadArea = document.getElementById('uploadArea');
        const fileInput = document.getElementById('fileInput');
        const fileInfo = document.getElementById('fileInfo');
        const processBtn = document.getElementById('processBtn');
        const uploadForm = document.getElementById('uploadForm');

        // 드래그앤드롭 이벤트
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });

        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });

        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                handleFile(files[0]);
            }
        });

        // 파일 선택 이벤트
        fileInput.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                handleFile(e.target.files[0]);
            }
        });

        // 파일 처리
        function handleFile(file) {
            // MIME 타입과 확장자 모두 검증 (호스팅 서버 호환성)
            const allowedTypes = [
                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                'application/vnd.ms-excel',
                'application/excel',
                'application/x-excel',
                'application/x-msexcel'
            ];

            const fileName = file.name.toLowerCase();
            const allowedExtensions = ['.xlsx', '.xls'];
            const hasValidExtension = allowedExtensions.some(ext => fileName.endsWith(ext));

            // MIME 타입 또는 확장자 중 하나라도 유효하면 통과
            if (!allowedTypes.includes(file.type) && !hasValidExtension) {
                alert(`엑셀 파일(.xlsx, .xls)만 업로드 가능합니다.\n\n파일 정보:\n- 이름: ${file.name}\n- 타입: ${file.type}\n- 크기: ${(file.size / 1024 / 1024).toFixed(2)} MB`);
                return;
            }

            // 디버깅 정보 (콘솔에 출력)
            console.log('파일 정보:', {
                name: file.name,
                type: file.type,
                size: file.size,
                hasValidExtension: hasValidExtension,
                typeValid: allowedTypes.includes(file.type)
            });

            document.getElementById('fileName').textContent = file.name;
            document.getElementById('fileSize').textContent = `크기: ${(file.size / 1024 / 1024).toFixed(2)} MB`;
            
            fileInfo.style.display = 'block';
            processBtn.style.display = 'block';
            
            // 파일을 폼에 설정
            const dataTransfer = new DataTransfer();
            dataTransfer.items.add(file);
            fileInput.files = dataTransfer.files;
        }

        // 폼 제출
        uploadForm.addEventListener('submit', async (e) => {
            e.preventDefault();
            
            if (!fileInput.files[0]) {
                alert('파일을 선택해주세요.');
                return;
            }

            const formData = new FormData();
            formData.append('excel_file', fileInput.files[0]);

            processBtn.textContent = '처리 중...';
            processBtn.disabled = true;

            try {
                const response = await fetch('init_excel_product_master.php', {
                    method: 'POST',
                    body: formData
                });

                const result = await response.text();
                document.getElementById('result').innerHTML = result;
                
            } catch (error) {
                document.getElementById('result').innerHTML = 
                    '<div class="result error">업로드 중 오류가 발생했습니다: ' + error.message + '</div>';
            } finally {
                processBtn.textContent = '🚀 데이터베이스에 업로드';
                processBtn.disabled = false;
            }
        });
    </script>
</body>
</html>

<?php
// PHP 처리 부분
use PhpOffice\PhpSpreadsheet\IOFactory;

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['excel_file'])) {
    require_once '../vendor/autoload.php';
    require_once '../conf/Database.php';
    
    try {
        // 데이터베이스 연결
        $db = new Database();
        
        // 업로드된 파일 처리
        $uploadedFile = $_FILES['excel_file']['tmp_name'];
        
        if (!file_exists($uploadedFile)) {
            throw new Exception('파일 업로드에 실패했습니다.');
        }
        
        // 엑셀 파일 읽기
        $spreadsheet = IOFactory::load($uploadedFile);
        $worksheet = $spreadsheet->getActiveSheet();
        $highestRow = $worksheet->getHighestRow();
        
        echo '<div class="result success">';
        echo '<h3>📊 엑셀 파일 처리 결과</h3>';
        echo '<p>총 ' . ($highestRow - 1) . '개의 데이터를 처리합니다.</p>';
        
        $successCount = 0;
        $errorCount = 0;
        $errors = [];
        
        // 트랜잭션 시작
        $db->beginTransaction();
        
        // 2행부터 데이터 처리 (1행은 헤더)
        for ($row = 2; $row <= $highestRow; $row++) {
            try {
                $optionNo = $worksheet->getCell('A' . $row)->getValue();
                $productName = $worksheet->getCell('B' . $row)->getValue();
                $initialAmount = $worksheet->getCell('C' . $row)->getValue();
                
                // 빈 행 건너뛰기
                if (empty($optionNo) && empty($productName) && empty($initialAmount)) {
                    continue;
                }
                
                // 데이터 삽입
                $data = [
                    'option_no' => $optionNo,
                    'product_name' => $productName,
                    'initial_amount' => $initialAmount
                ];
                
                $insertId = $db->insert('product_master', $data);
                $successCount++;
                
                echo '<p>✅ 행 ' . $row . ': ' . $productName . ' (ID: ' . $insertId . ')</p>';
                
            } catch (Exception $e) {
                $errorCount++;
                $errors[] = "행 {$row}: " . $e->getMessage();
                echo '<p>❌ 행 ' . $row . ': 오류 - ' . $e->getMessage() . '</p>';
            }
        }
        
        // 트랜잭션 커밋
        $db->commit();
        
        echo '<hr>';
        echo '<h4>📈 처리 완료</h4>';
        echo '<p>성공: ' . $successCount . '개</p>';
        echo '<p>실패: ' . $errorCount . '개</p>';
        
        if (!empty($errors)) {
            echo '<h5>오류 목록:</h5>';
            foreach ($errors as $error) {
                echo '<p style="color: red;">• ' . $error . '</p>';
            }
        }
        
        echo '</div>';
        
    } catch (Exception $e) {
        // 트랜잭션 롤백 (트랜잭션이 시작된 경우에만)
        if (isset($db)) {
            try {
                $db->rollback();
            } catch (Exception $rollbackException) {
                // 트랜잭션이 시작되지 않았거나 이미 종료된 경우 무시
                error_log('Rollback failed: ' . $rollbackException->getMessage());
            }
        }

        echo '<div class="result error">';
        echo '<h3>❌ 처리 실패</h3>';
        echo '<p>오류: ' . $e->getMessage() . '</p>';
        echo '</div>';
    }
}
?>
