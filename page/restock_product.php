<!DOCTYPE html>
<html lang="ko">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>상품 재입고 - 재고관리시스템</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 3px solid #17a2b8;
            padding-bottom: 10px;
        }
        .product-info {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
            border: 1px solid #dee2e6;
        }
        .form-group {
            margin-bottom: 20px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }
        .form-control {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            box-sizing: border-box;
            font-size: 16px;
        }
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin: 5px;
            font-size: 16px;
        }
        .btn-primary {
            background-color: #17a2b8;
            color: white;
        }
        .btn-secondary {
            background-color: #6c757d;
            color: white;
        }
        .btn:hover {
            opacity: 0.8;
        }
        .alert {
            padding: 15px;
            margin-bottom: 20px;
            border: 1px solid transparent;
            border-radius: 4px;
        }
        .alert-success {
            color: #155724;
            background-color: #d4edda;
            border-color: #c3e6cb;
        }
        .alert-danger {
            color: #721c24;
            background-color: #f8d7da;
            border-color: #f5c6cb;
        }
        .restock-history {
            margin-top: 40px;
        }
        .history-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        .history-table th,
        .history-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        .history-table th {
            background-color: #17a2b8;
            color: white;
            font-weight: bold;
        }
        .history-table tr:nth-child(even) {
            background-color: #f8f9fa;
        }
        .history-table tr:hover {
            background-color: #e3f2fd;
        }
        .positive {
            color: #28a745;
            font-weight: bold;
        }
        .negative {
            color: #dc3545;
            font-weight: bold;
        }
        .actions {
            text-align: center;
            margin-top: 30px;
        }
        .stock-summary {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .stock-item {
            text-align: center;
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 8px;
            border: 1px solid #dee2e6;
        }
        .stock-number {
            font-size: 2em;
            font-weight: bold;
            color: #17a2b8;
        }
        .stock-label {
            color: #6c757d;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <?php include '../components/navbar.php'; ?>
    
    <div class="container page-content">
        <h1>📦 상품 재입고</h1>
        
        <?php
        require_once '../conf/Database.php';

        $optionNo = $_GET['option_no'] ?? '';
        
        if (empty($optionNo)) {
            echo '<div class="alert alert-danger">상품 옵션번호가 지정되지 않았습니다.</div>';
            echo '<div class="actions"><a href="view_product_master.php" class="btn btn-secondary">목록으로 돌아가기</a></div>';
            echo '</div></body></html>';
            exit;
        }

        $db = new Database();

        // 메시지 표시 함수
        function showMessage($message, $type = 'info') {
            echo "<div class='alert alert-{$type}'>{$message}</div>";
        }

        // POST 요청 처리 (재입고 등록)
        if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['add_restock'])) {
            try {
                $restockAmount = intval($_POST['restock_amount']);
                $restockReason = $_POST['restock_reason'] ?? '';
                $notes = $_POST['notes'] ?? '';
                
                if ($restockAmount == 0) {
                    throw new Exception('재입고 수량은 0이 될 수 없습니다.');
                }
                
                $data = [
                    'option_no' => $optionNo,
                    'restock_amount' => $restockAmount,
                    'restock_reason' => $restockReason,
                    'notes' => $notes,
                    'restock_date' => date('Y-m-d H:i:s')
                ];
                
                $db->insert('product_restock', $data);
                
                $actionText = $restockAmount > 0 ? '입고' : '출고';
                showMessage("✅ {$actionText}가 성공적으로 등록되었습니다. (수량: {$restockAmount})", 'success');
                
            } catch (Exception $e) {
                showMessage("❌ 재입고 등록 실패: " . $e->getMessage(), 'danger');
            }
        }

        // 상품 정보 조회
        $product = $db->selectOne("SELECT * FROM product_master WHERE option_no = :option_no", ['option_no' => $optionNo]);
        
        if (!$product) {
            echo '<div class="alert alert-danger">상품을 찾을 수 없습니다.</div>';
            echo '<div class="actions"><a href="view_product_master.php" class="btn btn-secondary">목록으로 돌아가기</a></div>';
            echo '</div></body></html>';
            exit;
        }

        // 재고 정보 계산
        $orderSummary = $db->selectOne("
            SELECT COALESCE(SUM(order_amount), 0) as total_ordered 
            FROM orderForm 
            WHERE mapping_status = 'mapped' AND mapped_option_no = :option_no
        ", ['option_no' => $optionNo]);

        $restockSummary = $db->selectOne("
            SELECT COALESCE(SUM(restock_amount), 0) as total_restocked 
            FROM product_restock 
            WHERE option_no = :option_no
        ", ['option_no' => $optionNo]);

        $totalOrdered = $orderSummary['total_ordered'] ?? 0;
        $totalRestocked = $restockSummary['total_restocked'] ?? 0;
        $currentAmount = $product['initial_amount'] - $totalOrdered + $totalRestocked;

        // 상품 정보 표시
        echo '<div class="product-info">';
        echo '<h2>📋 상품 정보</h2>';
        echo '<p><strong>옵션번호:</strong> ' . htmlspecialchars($product['option_no']) . '</p>';
        echo '<p><strong>상품명:</strong> ' . htmlspecialchars($product['product_name']) . '</p>';
        echo '</div>';

        // 재고 요약
        echo '<div class="stock-summary">';
        echo '<div class="stock-item">';
        echo '<div class="stock-number">' . number_format($product['initial_amount']) . '</div>';
        echo '<div class="stock-label">초기수량</div>';
        echo '</div>';
        echo '<div class="stock-item">';
        echo '<div class="stock-number">' . number_format($totalOrdered) . '</div>';
        echo '<div class="stock-label">주문량</div>';
        echo '</div>';
        echo '<div class="stock-item">';
        echo '<div class="stock-number">' . number_format($totalRestocked) . '</div>';
        echo '<div class="stock-label">재입고량</div>';
        echo '</div>';
        echo '<div class="stock-item">';
        echo '<div class="stock-number" style="color: ' . ($currentAmount >= 0 ? '#28a745' : '#dc3545') . '">' . number_format($currentAmount) . '</div>';
        echo '<div class="stock-label">현재재고</div>';
        echo '</div>';
        echo '</div>';

        // 재입고 폼
        ?>
        <form method="POST">
            <h2>📦 재입고 등록</h2>
            
            <div class="form-group">
                <label for="restock_amount">재입고 수량 *</label>
                <input type="number" 
                       id="restock_amount" 
                       name="restock_amount" 
                       class="form-control" 
                       required
                       placeholder="양수: 입고, 음수: 출고">
                <small style="color: #6c757d;">양수는 입고, 음수는 출고를 의미합니다.</small>
            </div>
            
            <div class="form-group">
                <label for="restock_reason">재입고 사유</label>
                <select id="restock_reason" name="restock_reason" class="form-control">
                    <option value="">사유 선택...</option>
                    <option value="신규입고">신규입고</option>
                    <option value="추가입고">추가입고</option>
                    <option value="반품입고">반품입고</option>
                    <option value="재고조정">재고조정</option>
                    <option value="불량품출고">불량품출고</option>
                    <option value="기타">기타</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="notes">비고</label>
                <textarea id="notes" 
                         name="notes" 
                         class="form-control" 
                         rows="3"
                         placeholder="추가 설명이나 메모를 입력하세요"></textarea>
            </div>
            
            <div class="actions">
                <button type="submit" name="add_restock" class="btn btn-primary">재입고 등록</button>
                <a href="view_product_master.php" class="btn btn-secondary">목록으로 돌아가기</a>
            </div>
        </form>

        <?php
        // 재입고 이력 조회
        $restockHistory = $db->select("
            SELECT * FROM product_restock 
            WHERE option_no = :option_no 
            ORDER BY restock_date DESC
        ", ['option_no' => $optionNo]);

        echo '<div class="restock-history">';
        echo '<h2>📊 재입고 이력 (' . count($restockHistory) . '건)</h2>';
        
        if (empty($restockHistory)) {
            echo '<p style="text-align: center; color: #6c757d; padding: 40px;">재입고 이력이 없습니다.</p>';
        } else {
            echo '<table class="history-table">';
            echo '<thead>';
            echo '<tr>';
            echo '<th>일시</th>';
            echo '<th>수량</th>';
            echo '<th>사유</th>';
            echo '<th>비고</th>';
            echo '</tr>';
            echo '</thead>';
            echo '<tbody>';
            
            foreach ($restockHistory as $history) {
                $amountClass = $history['restock_amount'] > 0 ? 'positive' : 'negative';
                $amountText = $history['restock_amount'] > 0 ? '+' . number_format($history['restock_amount']) : number_format($history['restock_amount']);
                
                echo '<tr>';
                echo '<td>' . date('Y-m-d H:i', strtotime($history['restock_date'])) . '</td>';
                echo '<td class="' . $amountClass . '">' . $amountText . '</td>';
                echo '<td>' . htmlspecialchars($history['restock_reason'] ?? '-') . '</td>';
                echo '<td>' . htmlspecialchars($history['notes'] ?? '-') . '</td>';
                echo '</tr>';
            }
            
            echo '</tbody>';
            echo '</table>';
        }
        echo '</div>';
        ?>
    </div>
</body>
</html>
