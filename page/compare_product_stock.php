<!DOCTYPE html>
<html lang="ko">
<head> 
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>상품 연결 - 재고관리시스템</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 3px solid #007cba;
            padding-bottom: 10px;
        }
        .section {
            margin-bottom: 40px;
        }
        .section h2 {
            color: #007cba;
            border-left: 4px solid #007cba;
            padding-left: 10px;
            margin-bottom: 20px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #007cba;
            color: white;
            font-weight: bold;
            position: sticky;
            top: 0;
        }
        tr:nth-child(even) {
            background-color: #f8f9fa;
        }
        tr:hover {
            background-color: #e3f2fd;
        }
        .btn {
            padding: 8px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin: 2px;
        }
        .btn-primary {
            background-color: #007cba;
            color: white;
        }
        .btn-success {
            background-color: #28a745;
            color: white;
        }
        .btn-danger {
            background-color: #dc3545;
            color: white;
        }
        .btn-warning {
            background-color: #ffc107;
            color: #212529;
        }
        .btn:hover {
            opacity: 0.8;
        }
        .form-group {
            margin-bottom: 15px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .form-control {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        .alert {
            padding: 15px;
            margin-bottom: 20px;
            border: 1px solid transparent;
            border-radius: 4px;
        }
        .alert-success {
            color: #155724;
            background-color: #d4edda;
            border-color: #c3e6cb;
        }
        .alert-danger {
            color: #721c24;
            background-color: #f8d7da;
            border-color: #f5c6cb;
        }
        .alert-info {
            color: #0c5460;
            background-color: #d1ecf1;
            border-color: #bee5eb;
        }
        .no-data {
            text-align: center;
            padding: 50px;
            color: #6c757d;
            font-size: 18px;
        }
        .stats-box {
            display: flex;
            justify-content: space-around;
            margin-bottom: 30px;
        }
        .stat-item {
            text-align: center;
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 8px;
            border: 1px solid #dee2e6;
        }
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #007cba;
        }
        .stat-label {
            color: #6c757d;
            margin-top: 5px;
        }
        .actions {
            margin-bottom: 20px;
        }
        .actions a {
            display: inline-block;
            padding: 10px 20px;
            background-color: #28a745;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            margin-right: 10px;
        }
        .actions a:hover {
            background-color: #218838;
        }
        .mapping-form {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 10px;
        }
        .mapping-form select {
            width: 200px;
            padding: 5px;
            margin-right: 10px;
        }
        .product-info {
            font-weight: bold;
            color: #495057;
        }
        .order-info {
            color: #6c757d;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <?php include '../components/navbar.php'; ?>

    <div class="container page-content">
        <h1>🔗 상품 연결 관리</h1>

<?php
require_once '../conf/Database.php';

// 메시지 표시 함수

function showMessage($message, $type = 'info') {
    echo "<div class='alert alert-{$type}'>{$message}</div>";
}

try {
    $db = new Database();
    
    // POST 요청 처리
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        if (isset($_POST['action'])) {
            switch ($_POST['action']) {
                case 'create_mapping':
                    // 새로운 매핑 생성
                    $productNameSur = $_POST['product_name_sur'];
                    $productNameOption = $_POST['product_name_option'];

                    // 옵션번호 결정 (선택 또는 직접입력)
                    $optionNo = '';
                    if (!empty($_POST['option_no'])) {
                        $optionNo = $_POST['option_no'];
                    } elseif (!empty($_POST['option_no_direct'])) {
                        $optionNo = trim($_POST['option_no_direct']);
                    }

                    if (empty($optionNo)) {
                        showMessage("❌ 연결할 상품을 선택하거나 옵션번호를 입력해주세요.", 'danger');
                        break;
                    }
                    
                    // product_mapping 테이블에 매핑 정보 저장
                    $mappingData = [
                        'product_name_sur' => $productNameSur,
                        'product_name_option' => $productNameOption,
                        'option_no' => $optionNo
                    ];
                    
                    try {
                        $db->insert('product_mapping', $mappingData);
                        
                        // orderForm 테이블의 해당 주문건들을 매핑된 상태로 업데이트
                        $updateSql = "UPDATE orderForm 
                                     SET mapping_status = 'mapped', mapped_option_no = :option_no 
                                     WHERE product_name_sur = :product_name_sur 
                                     AND product_name_option = :product_name_option 
                                     AND mapping_status = 'unmapped'";
                        
                        $updateParams = [
                            'option_no' => $optionNo,
                            'product_name_sur' => $productNameSur,
                            'product_name_option' => $productNameOption
                        ];
                        
                        $stmt = $db->connect()->prepare($updateSql);
                        $stmt->execute($updateParams);
                        $updatedRows = $stmt->rowCount();
                        
                        showMessage("✅ 매핑이 성공적으로 생성되었습니다. {$updatedRows}개의 주문건이 연결되었습니다.", 'success');
                        
                    } catch (Exception $e) {
                        if (strpos($e->getMessage(), 'Duplicate entry') !== false) {
                            showMessage("❌ 이미 존재하는 매핑입니다.", 'danger');
                        } else {
                            showMessage("❌ 매핑 생성 실패: " . $e->getMessage(), 'danger');
                        }
                    }
                    break;

                case 'bulk_create_mapping':
                    // 일괄 매핑 생성 (AJAX 요청용)
                    $productNameSur = $_POST['product_name_sur'];
                    $productNameOption = $_POST['product_name_option'];
                    $optionNo = trim($_POST['option_no']);

                    try {
                        // 상품 마스터에 해당 옵션번호가 있는지 확인
                        $product = $db->selectOne("SELECT * FROM product_master WHERE option_no = :option_no", ['option_no' => $optionNo]);

                        if (!$product) {
                            echo "❌ 옵션번호 '{$optionNo}'에 해당하는 상품을 찾을 수 없습니다.";
                            exit;
                        }

                        // 이미 매핑이 있는지 확인
                        $existingMapping = $db->selectOne("
                            SELECT * FROM product_mapping
                            WHERE product_name_sur = :product_name_sur
                            AND product_name_option = :product_name_option
                        ", [
                            'product_name_sur' => $productNameSur,
                            'product_name_option' => $productNameOption
                        ]);

                        if ($existingMapping) {
                            echo "⚠️ 이미 매핑이 존재합니다.";
                            exit;
                        }

                        // 새로운 매핑 생성
                        $mappingData = [
                            'product_name_sur' => $productNameSur,
                            'product_name_option' => $productNameOption,
                            'mapped_option_no' => $optionNo,
                            'mapped_product_name' => $product['product_name']
                        ];

                        $db->insert('product_mapping', $mappingData);

                        // orderForm 테이블 업데이트
                        $updateOrderSql = "UPDATE orderForm
                                          SET mapping_status = 'mapped', mapped_option_no = :option_no
                                          WHERE product_name_sur = :product_name_sur
                                          AND product_name_option = :product_name_option
                                          AND mapping_status = 'unmapped'";

                        $updateParams = [
                            'option_no' => $optionNo,
                            'product_name_sur' => $productNameSur,
                            'product_name_option' => $productNameOption
                        ];

                        $stmt = $db->connect()->prepare($updateOrderSql);
                        $stmt->execute($updateParams);

                        echo "✅ 매핑 성공";

                    } catch (Exception $e) {
                        echo "❌ 오류: " . $e->getMessage();
                    }
                    exit;

                case 'update_mapping':
                    // 기존 매핑 수정
                    $mappingId = $_POST['mapping_id'];
                    $optionNo = $_POST['option_no'];
                    
                    $updateData = ['option_no' => $optionNo];
                    $db->update('product_mapping', $updateData, 'id = :id', ['id' => $mappingId]);
                    
                    // orderForm 테이블도 업데이트
                    $mapping = $db->selectOne("SELECT * FROM product_mapping WHERE id = :id", ['id' => $mappingId]);
                    if ($mapping) {
                        $updateOrderSql = "UPDATE orderForm 
                                          SET mapped_option_no = :option_no 
                                          WHERE product_name_sur = :product_name_sur 
                                          AND product_name_option = :product_name_option 
                                          AND mapping_status = 'mapped'";
                        
                        $updateOrderParams = [
                            'option_no' => $optionNo,
                            'product_name_sur' => $mapping['product_name_sur'],
                            'product_name_option' => $mapping['product_name_option']
                        ];
                        
                        $stmt = $db->connect()->prepare($updateOrderSql);
                        $stmt->execute($updateOrderParams);
                    }
                    
                    showMessage("✅ 매핑이 성공적으로 수정되었습니다.", 'success');
                    break;
                    
                case 'delete_mapping':
                    // 매핑 삭제
                    $mappingId = $_POST['mapping_id'];
                    
                    // 먼저 매핑 정보 조회
                    $mapping = $db->selectOne("SELECT * FROM product_mapping WHERE id = :id", ['id' => $mappingId]);
                    
                    if ($mapping) {
                        // orderForm 테이블을 unmapped 상태로 변경
                        $updateOrderSql = "UPDATE orderForm 
                                          SET mapping_status = 'unmapped', mapped_option_no = NULL 
                                          WHERE product_name_sur = :product_name_sur 
                                          AND product_name_option = :product_name_option 
                                          AND mapping_status = 'mapped'";
                        
                        $updateOrderParams = [
                            'product_name_sur' => $mapping['product_name_sur'],
                            'product_name_option' => $mapping['product_name_option']
                        ];
                        
                        $stmt = $db->connect()->prepare($updateOrderSql);
                        $stmt->execute($updateOrderParams);
                        
                        // 매핑 삭제
                        $db->delete('product_mapping', 'id = :id', ['id' => $mappingId]);
                        
                        showMessage("✅ 매핑이 성공적으로 삭제되었습니다.", 'success');
                    }
                    break;
            }
        }
    }
    
    // 통계 정보 조회
    $totalOrders = $db->selectOne("SELECT COUNT(*) as count FROM orderForm")['count'];
    $mappedOrders = $db->selectOne("SELECT COUNT(*) as count FROM orderForm WHERE mapping_status = 'mapped'")['count'];
    $unmappedOrders = $totalOrders - $mappedOrders;
    $totalMappings = $db->selectOne("SELECT COUNT(*) as count FROM product_mapping")['count'];
    
    echo '<div class="stats-box">';
    echo '<div class="stat-item">';
    echo '<div class="stat-number">' . number_format($totalOrders) . '</div>';
    echo '<div class="stat-label">전체 주문건</div>';
    echo '</div>';
    echo '<div class="stat-item">';
    echo '<div class="stat-number">' . number_format($mappedOrders) . '</div>';
    echo '<div class="stat-label">연결된 주문건</div>';
    echo '</div>';
    echo '<div class="stat-item">';
    echo '<div class="stat-number">' . number_format($unmappedOrders) . '</div>';
    echo '<div class="stat-label">미연결 주문건</div>';
    echo '</div>';
    echo '<div class="stat-item">';
    echo '<div class="stat-number">' . number_format($totalMappings) . '</div>';
    echo '<div class="stat-label">매핑 규칙</div>';
    echo '</div>';
    echo '</div>';
    
    // 미연결 주문건 조회 (중복 제거)
    $unmappedOrdersSql = "
        SELECT 
            product_name_sur,
            product_name_option,
            COUNT(*) as order_count,
            SUM(order_amount) as total_amount,
            GROUP_CONCAT(DISTINCT filename ORDER BY filename) as filenames
        FROM orderForm 
        WHERE mapping_status = 'unmapped' 
        GROUP BY product_name_sur, product_name_option
        ORDER BY product_name_sur, product_name_option
    ";
    
    $unmappedOrders = $db->select($unmappedOrdersSql);
    
    // 상품 마스터 목록 조회 (매핑용)
    $productMasters = $db->select("SELECT option_no, product_name FROM product_master ORDER BY option_no");
    
    echo '<div class="section">';
    echo '<h2>🔍 미연결 주문건 (' . count($unmappedOrders) . '개)</h2>';

    if (empty($unmappedOrders)) {
        echo '<div class="no-data">🎉 모든 주문건이 연결되었습니다!</div>';
    } else {
        echo '<div class="alert alert-info">';
        echo '<strong>안내:</strong> 아래 주문건들을 상품 마스터와 연결해주세요. 연결 후 해당 상품의 재고가 자동으로 계산됩니다.';
        echo '</div>';

        // 일괄 연결 버튼 (상단)
        echo '<div style="margin-bottom: 20px; text-align: center;">';
        echo '<button type="button" class="btn" style="background-color: #28a745; color: white; padding: 12px 24px; font-size: 16px;" onclick="bulkConnect()">📦 일괄 연결</button>';
        echo '<button type="button" class="btn" style="background-color: #6c757d; color: white; padding: 8px 16px; font-size: 14px; margin-left: 10px;" onclick="debugForms()">🔍 디버그</button>';
        echo '<br><span style="color: #6c757d;">※ 연결할 상품 또는 옵션번호가 입력된 항목들을 일괄로 연결합니다</span>';
        echo '</div>';
        
        echo '<table>';
        echo '<thead>';
        echo '<tr>';
        echo '<th>상품명</th>';
        echo '<th>옵션명</th>';
        echo '<th>주문건수</th>';
        echo '<th>총 주문량</th>';
        echo '<th>파일명</th>';
        echo '<th>연결할 상품</th>';
        echo '<th>연결할 상품-옵션번호</th>';
        echo '<th>작업</th>';
        echo '</tr>';
        echo '</thead>';
        echo '<tbody>';
        
        foreach ($unmappedOrders as $order) {
            echo '<tr>';
            echo '<td class="product-info">' . htmlspecialchars($order['product_name_sur']) . '</td>';
            echo '<td class="product-info">' . htmlspecialchars($order['product_name_option']) . '</td>';
            echo '<td>' . number_format($order['order_count']) . '건</td>';
            echo '<td>' . number_format($order['total_amount']) . '</td>';
            echo '<td class="order-info">' . htmlspecialchars($order['filenames']) . '</td>';
            echo '<td>';
            echo '<form method="POST" class="mapping-form" style="margin: 0;">';
            echo '<input type="hidden" name="action" value="create_mapping">';
            echo '<input type="hidden" name="product_name_sur" value="' . htmlspecialchars($order['product_name_sur']) . '">';
            echo '<input type="hidden" name="product_name_option" value="' . htmlspecialchars($order['product_name_option']) . '">';
            echo '<select name="option_no" onchange="clearOptionInput(this)">';
            echo '<option value="">상품 선택...</option>';
            foreach ($productMasters as $product) {
                echo '<option value="' . htmlspecialchars($product['option_no']) . '">';
                echo htmlspecialchars($product['option_no']) . ' - ' . htmlspecialchars($product['product_name']);
                echo '</option>';
            }
            echo '</select>';
            echo '</td>';
            echo '<td>';
            echo '<input type="text" name="option_no_direct" placeholder="옵션번호 직접입력" onchange="clearProductSelect(this)" style="width: 150px; padding: 5px; border: 1px solid #ddd; border-radius: 3px;">';
            echo '</td>';
            echo '<td>';
            echo '<button type="submit" class="btn btn-success">연결</button>';
            echo '</form>';
            echo '</td>';
            echo '</tr>';
        }
        
        echo '</tbody>';
        echo '</table>';

        // 일괄 연결 버튼 (하단)
        echo '<div style="margin-top: 20px; text-align: center;">';
        echo '<button type="button" class="btn" style="background-color: #28a745; color: white; padding: 12px 24px; font-size: 16px;" onclick="bulkConnect()">📦 일괄 연결</button>';
        echo '<span style="margin-left: 15px; color: #6c757d;">※ 연결할 상품 또는 옵션번호가 입력된 항목들을 일괄로 연결합니다</span>';
        echo '</div>';
    }
    echo '</div>';

    // 연결된 매핑 목록 조회
    $mappedListSql = "
        SELECT
            pm.id,
            pm.product_name_sur,
            pm.product_name_option,
            pm.option_no,
            pmaster.product_name as master_product_name,
            COUNT(o.id) as order_count,
            SUM(o.order_amount) as total_amount,
            pm.created_at
        FROM product_mapping pm
        LEFT JOIN product_master pmaster ON pm.option_no = pmaster.option_no
        LEFT JOIN orderForm o ON pm.product_name_sur = o.product_name_sur
                               AND pm.product_name_option = o.product_name_option
                               AND o.mapping_status = 'mapped'
        GROUP BY pm.id, pm.product_name_sur, pm.product_name_option, pm.option_no, pmaster.product_name, pm.created_at
        ORDER BY pm.created_at DESC
    ";

    $mappedList = $db->select($mappedListSql);

    echo '<div class="section">';
    echo '<h2>✅ 연결된 매핑 목록 (' . count($mappedList) . '개)</h2>';

    if (empty($mappedList)) {
        echo '<div class="no-data">연결된 매핑이 없습니다.</div>';
    } else {
        echo '<table>';
        echo '<thead>';
        echo '<tr>';
        echo '<th>상품명</th>';
        echo '<th>옵션명</th>';
        echo '<th>연결된 상품</th>';
        echo '<th>옵션번호</th>';
        echo '<th>주문건수</th>';
        echo '<th>총 주문량</th>';
        echo '<th>생성일</th>';
        echo '<th>작업</th>';
        echo '</tr>';
        echo '</thead>';
        echo '<tbody>';

        foreach ($mappedList as $mapping) {
            echo '<tr>';
            echo '<td class="product-info">' . htmlspecialchars($mapping['product_name_sur']) . '</td>';
            echo '<td class="product-info">' . htmlspecialchars($mapping['product_name_option']) . '</td>';
            echo '<td>' . htmlspecialchars($mapping['master_product_name'] ?? '상품 없음') . '</td>';
            echo '<td>' . htmlspecialchars($mapping['option_no']) . '</td>';
            echo '<td>' . number_format($mapping['order_count']) . '건</td>';
            echo '<td>' . number_format($mapping['total_amount']) . '</td>';
            echo '<td>' . date('Y-m-d H:i', strtotime($mapping['created_at'])) . '</td>';
            echo '<td>';

            // 수정 폼
            echo '<form method="POST" style="display: inline-block; margin-right: 5px;">';
            echo '<input type="hidden" name="action" value="update_mapping">';
            echo '<input type="hidden" name="mapping_id" value="' . $mapping['id'] . '">';
            echo '<select name="option_no" onchange="this.form.submit()" style="width: 150px; padding: 3px;">';
            foreach ($productMasters as $product) {
                $selected = ($product['option_no'] == $mapping['option_no']) ? 'selected' : '';
                echo '<option value="' . htmlspecialchars($product['option_no']) . '" ' . $selected . '>';
                echo htmlspecialchars($product['option_no']) . ' - ' . htmlspecialchars($product['product_name']);
                echo '</option>';
            }
            echo '</select>';
            echo '</form>';

            // 삭제 버튼
            echo '<form method="POST" style="display: inline-block;" onsubmit="return confirm(\'정말 삭제하시겠습니까?\')">';
            echo '<input type="hidden" name="action" value="delete_mapping">';
            echo '<input type="hidden" name="mapping_id" value="' . $mapping['id'] . '">';
            echo '<button type="submit" class="btn btn-danger">삭제</button>';
            echo '</form>';

            echo '</td>';
            echo '</tr>';
        }

        echo '</tbody>';
        echo '</table>';
    }
    echo '</div>';

} catch (Exception $e) {
    showMessage("❌ 오류 발생: " . $e->getMessage(), 'danger');
}
?>

    </div>

    <script>
        // 상품 선택 시 옵션번호 입력 필드 초기화
        function clearOptionInput(selectElement) {
            if (selectElement.value) {
                const row = selectElement.closest('tr');
                const optionInput = row.querySelector('input[name="option_no_direct"]');
                if (optionInput) {
                    optionInput.value = '';
                }
            }
        }

        // 옵션번호 입력 시 상품 선택 초기화
        function clearProductSelect(inputElement) {
            if (inputElement.value) {
                const row = inputElement.closest('tr');
                const productSelect = row.querySelector('select[name="option_no"]');
                if (productSelect) {
                    productSelect.value = '';
                }
            }
        }

        // 일괄 연결 함수
        async function bulkConnect() {
            const forms = document.querySelectorAll('.mapping-form');
            const connectionsToMake = [];

            console.log('총 폼 개수:', forms.length); // 디버깅용

            // 연결할 항목들 수집
            forms.forEach((form, index) => {
                const productSelect = form.querySelector('select[name="option_no"]');
                const optionInput = form.querySelector('input[name="option_no_direct"]');
                const productNameSur = form.querySelector('input[name="product_name_sur"]').value;
                const productNameOption = form.querySelector('input[name="product_name_option"]').value;

                let optionNo = '';

                // 상품 선택 또는 옵션번호 직접입력 중 하나가 있는지 확인
                if (productSelect && productSelect.value && productSelect.value !== '') {
                    optionNo = productSelect.value;
                    console.log(`폼 ${index + 1}: 상품 선택됨 - ${optionNo}`); // 디버깅용
                } else if (optionInput && optionInput.value && optionInput.value.trim() !== '') {
                    optionNo = optionInput.value.trim();
                    console.log(`폼 ${index + 1}: 옵션번호 입력됨 - ${optionNo}`); // 디버깅용
                } else {
                    console.log(`폼 ${index + 1}: 입력값 없음`); // 디버깅용
                }

                if (optionNo) {
                    connectionsToMake.push({
                        form: form,
                        optionNo: optionNo,
                        productNameSur: productNameSur,
                        productNameOption: productNameOption
                    });
                }
            });

            console.log('연결할 항목 수:', connectionsToMake.length); // 디버깅용

            if (connectionsToMake.length === 0) {
                alert('연결할 항목이 없습니다.\n\n다음 중 하나를 수행해주세요:\n1. 드롭다운에서 상품을 선택하거나\n2. "연결할 상품-옵션번호" 필드에 옵션번호를 직접 입력해주세요.\n\n※ 일부 상품만 입력해도 해당 상품들만 연결됩니다.');
                return;
            }

            const confirmMessage = `총 ${connectionsToMake.length}개 항목을 일괄 연결하시겠습니까?\n\n연결될 항목들:\n` +
                connectionsToMake.map((item, index) =>
                    `${index + 1}. ${item.productNameSur} ${item.productNameOption} → ${item.optionNo}`
                ).join('\n');

            if (!confirm(confirmMessage)) {
                return;
            }

            // 일괄 연결 실행
            let successCount = 0;
            let errorCount = 0;
            const errors = [];

            for (const connection of connectionsToMake) {
                try {
                    const formData = new FormData();
                    formData.append('action', 'bulk_create_mapping');
                    formData.append('option_no', connection.optionNo);
                    formData.append('product_name_sur', connection.productNameSur);
                    formData.append('product_name_option', connection.productNameOption);

                    const response = await fetch(window.location.href, {
                        method: 'POST',
                        body: formData
                    });

                    const result = await response.text();

                    if (result.includes('성공') || result.includes('완료')) {
                        successCount++;
                    } else {
                        errorCount++;
                        errors.push(`${connection.productNameSur} ${connection.productNameOption}: 연결 실패`);
                    }
                } catch (error) {
                    errorCount++;
                    errors.push(`${connection.productNameSur} ${connection.productNameOption}: ${error.message}`);
                }
            }

            // 결과 메시지
            let resultMessage = `일괄 연결 완료!\n\n성공: ${successCount}개\n실패: ${errorCount}개`;

            if (errors.length > 0) {
                resultMessage += '\n\n실패 항목:\n' + errors.join('\n');
            }

            alert(resultMessage);

            // 페이지 새로고침
            if (successCount > 0) {
                window.location.reload();
            }
        }

        // 디버그 함수
        function debugForms() {
            const forms = document.querySelectorAll('.mapping-form');
            console.log('=== 폼 디버그 정보 ===');
            console.log('총 폼 개수:', forms.length);

            let debugInfo = `총 ${forms.length}개의 폼이 발견되었습니다.\n\n`;

            forms.forEach((form, index) => {
                const productSelect = form.querySelector('select[name="option_no"]');
                const optionInput = form.querySelector('input[name="option_no_direct"]');
                const productNameSur = form.querySelector('input[name="product_name_sur"]');
                const productNameOption = form.querySelector('input[name="product_name_option"]');

                debugInfo += `폼 ${index + 1}:\n`;
                debugInfo += `  상품명: ${productNameSur ? productNameSur.value : 'null'}\n`;
                debugInfo += `  옵션명: ${productNameOption ? productNameOption.value : 'null'}\n`;
                debugInfo += `  선택된 상품: ${productSelect ? productSelect.value : 'null'}\n`;
                debugInfo += `  입력된 옵션번호: ${optionInput ? optionInput.value : 'null'}\n`;
                debugInfo += `\n`;

                console.log(`폼 ${index + 1}:`, {
                    productSelect: productSelect ? productSelect.value : null,
                    optionInput: optionInput ? optionInput.value : null,
                    productNameSur: productNameSur ? productNameSur.value : null,
                    productNameOption: productNameOption ? productNameOption.value : null
                });
            });

            alert(debugInfo);
        }
    </script>
</body>
</html>
