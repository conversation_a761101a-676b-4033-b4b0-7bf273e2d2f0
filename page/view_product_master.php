<!DOCTYPE html>
<html lang="ko">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>상품 마스터 조회</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 3px solid #007cba;
            padding-bottom: 10px;
        }
        .search-box {
            margin-bottom: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
            border: 1px solid #dee2e6;
        }
        .search-box input {
            padding: 8px 12px;
            border: 1px solid #ccc;
            border-radius: 4px;
            margin-right: 10px;
            width: 200px;
        }
        .search-box button {
            padding: 8px 15px;
            background-color: #007cba;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .search-box button:hover {
            background-color: #005a8b;
        }
        .info-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding: 10px;
            background-color: #e9ecef;
            border-radius: 5px;
        }
        .total-count {
            font-weight: bold;
            color: #495057;
        }
        .per-page-selector select {
            padding: 5px;
            border: 1px solid #ccc;
            border-radius: 4px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #007cba;
            color: white;
            font-weight: bold;
            position: sticky;
            top: 0;
        }
        tr:nth-child(even) {
            background-color: #f8f9fa;
        }
        tr:hover {
            background-color: #e3f2fd;
        }
        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            margin-top: 20px;
            gap: 5px;
        }
        .pagination a, .pagination span {
            padding: 8px 12px;
            text-decoration: none;
            border: 1px solid #ddd;
            color: #007cba;
            border-radius: 4px;
        }
        .pagination a:hover {
            background-color: #e3f2fd;
        }
        .pagination .current {
            background-color: #007cba;
            color: white;
            border-color: #007cba;
        }
        .pagination .disabled {
            color: #6c757d;
            cursor: not-allowed;
        }
        .no-data {
            text-align: center;
            padding: 50px;
            color: #6c757d;
            font-size: 18px;
        }
        .actions {
            margin-bottom: 20px;
        }
        .actions a {
            display: inline-block;
            padding: 10px 20px;
            background-color: #28a745;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            margin-right: 10px;
        }
        .actions a:hover {
            background-color: #218838;
        }
        .btn {
            padding: 5px 10px;
            text-decoration: none;
            border-radius: 3px;
            font-size: 12px;
            display: inline-block;
        }
        .btn-warning {
            background-color: #ffc107;
            color: #212529;
        }
        .btn-danger {
            background-color: #dc3545;
            color: white;
        }
        .btn:hover {
            opacity: 0.8;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📦 상품 마스터 조회</h1>
        
        <div class="actions">
            <a href="../util/init_excel_product_master.php">📤 엑셀 업로드</a>
            <a href="upload_orderform_excel.php">📋 주문서 업로드</a>
            <a href="compare_product_stock.php">🔗 상품 연결 관리</a>
            <a href="manage_product.php?action=add" style="background-color: #007cba;">➕ 새 상품 추가</a>
        </div>

        <div class="search-box">
            <form method="GET" action="">
                <input type="text" name="search" placeholder="상품명 또는 옵션번호 검색..." 
                       value="<?php echo htmlspecialchars($_GET['search'] ?? ''); ?>">
                <button type="submit">🔍 검색</button>
                <?php if (!empty($_GET['search'])): ?>
                    <a href="?" style="margin-left: 10px; color: #dc3545; text-decoration: none;">✖ 검색 초기화</a>
                <?php endif; ?>
                <input type="hidden" name="per_page" value="<?php echo htmlspecialchars($_GET['per_page'] ?? '50'); ?>">
            </form>
        </div>

<?php
require_once '../conf/Database.php';

try {
    // 데이터베이스 연결
    $db = new Database();
    
    // 페이지네이션 설정 (기본값을 전체로 변경)
    $page = max(1, intval($_GET['page'] ?? 1));
    $perPage = intval($_GET['per_page'] ?? 0); // 0이면 전체 보기
    if ($perPage > 0) {
        $perPage = max(10, min(200, $perPage)); // 10~200 사이로 제한
        $offset = ($page - 1) * $perPage;
    } else {
        $offset = 0; // 전체 보기
    }
    
    // 검색 조건
    $search = trim($_GET['search'] ?? '');
    $whereClause = '';
    $params = [];
    
    if (!empty($search)) {
        $whereClause = "WHERE option_no LIKE :search OR product_name LIKE :search";
        $params['search'] = "%{$search}%";
    }
    
    // 전체 레코드 수 조회
    $countSql = "SELECT COUNT(*) as total FROM product_master {$whereClause}";
    $totalResult = $db->selectOne($countSql, $params);
    $totalRecords = $totalResult['total'];
    $totalPages = ceil($totalRecords / $perPage);
    
    // 데이터 조회 (현재 재고량 계산 포함)
    $baseSql = "
        SELECT
            pm.*,
            COALESCE(pm.initial_amount, 0) as initial_amount,
            COALESCE(order_summary.total_ordered, 0) as total_ordered,
            (COALESCE(pm.initial_amount, 0) - COALESCE(order_summary.total_ordered, 0)) as current_amount
        FROM product_master pm
        LEFT JOIN (
            SELECT
                mapped_option_no,
                SUM(order_amount) as total_ordered
            FROM orderForm
            WHERE mapping_status = 'mapped' AND mapped_option_no IS NOT NULL
            GROUP BY mapped_option_no
        ) order_summary ON pm.option_no = order_summary.mapped_option_no
        {$whereClause}
        ORDER BY pm.option_no ASC
    ";

    if ($perPage > 0) {
        $sql = $baseSql . " LIMIT :limit OFFSET :offset";
        $params['limit'] = $perPage;
        $params['offset'] = $offset;
    } else {
        $sql = $baseSql; // 전체 보기
    }

    $products = $db->select($sql, $params);
    
    echo '<div class="info-bar">';
    echo '<div class="total-count">총 ' . number_format($totalRecords) . '개 상품';
    if (!empty($search)) {
        echo ' (검색: "' . htmlspecialchars($search) . '")';
    }
    echo '</div>';
    echo '<div class="per-page-selector">';
    echo '<form method="GET" style="display: inline;">';
    if (!empty($search)) {
        echo '<input type="hidden" name="search" value="' . htmlspecialchars($search) . '">';
    }
    echo '<label>페이지당 표시: ';
    echo '<select name="per_page" onchange="this.form.submit()">';
    $allSelected = ($perPage == 0) ? 'selected' : '';
    echo "<option value=\"0\" {$allSelected}>전체</option>";
    foreach ([10, 25, 50, 100, 200] as $option) {
        $selected = ($perPage == $option) ? 'selected' : '';
        echo "<option value=\"{$option}\" {$selected}>{$option}개</option>";
    }
    echo '</select>';
    echo '</label>';
    echo '</form>';
    echo '</div>';
    echo '</div>';
    
    if (empty($products)) {
        echo '<div class="no-data">';
        if (!empty($search)) {
            echo '🔍 검색 결과가 없습니다.';
        } else {
            echo '📦 등록된 상품이 없습니다.';
        }
        echo '</div>';
    } else {
        echo '<table>';
        echo '<thead>';
        echo '<tr>';
        echo '<th>옵션번호</th>';
        echo '<th>상품명</th>';
        echo '<th>초기수량</th>';
        echo '<th>주문량</th>';
        echo '<th>현재재고</th>';
        echo '<th>재고상태</th>';
        echo '<th>작업</th>';
        echo '</tr>';
        echo '</thead>';
        echo '<tbody>';

        foreach ($products as $product) {
            $currentAmount = $product['current_amount'];
            $stockStatus = '';
            $stockColor = '';

            if ($currentAmount <= 0) {
                $stockStatus = '품절';
                $stockColor = 'color: #dc3545; font-weight: bold;';
            } elseif ($currentAmount <= 10) {
                $stockStatus = '부족';
                $stockColor = 'color: #fd7e14; font-weight: bold;';
            } else {
                $stockStatus = '충분';
                $stockColor = 'color: #28a745;';
            }

            echo '<tr>';
            echo '<td>' . htmlspecialchars($product['option_no']) . '</td>';
            echo '<td>' . htmlspecialchars($product['product_name']) . '</td>';
            echo '<td>' . number_format($product['initial_amount']) . '</td>';
            echo '<td>' . number_format($product['total_ordered']) . '</td>';
            echo '<td style="' . $stockColor . '">' . number_format($currentAmount) . '</td>';
            echo '<td style="' . $stockColor . '">' . $stockStatus . '</td>';
            echo '<td>';
            echo '<a href="manage_product.php?action=edit&id=' . $product['option_no'] . '" class="btn btn-warning" style="margin-right: 5px;">수정</a>';
            echo '<a href="manage_product.php?action=delete&id=' . $product['option_no'] . '" class="btn btn-danger">삭제</a>';
            echo '</td>';
            echo '</tr>';
        }
        
        echo '</tbody>';
        echo '</table>';
        
        // 페이지네이션 (전체 보기가 아닐 때만)
        if ($perPage > 0 && $totalPages > 1) {
            echo '<div class="pagination">';
            
            // 이전 페이지
            if ($page > 1) {
                $prevPage = $page - 1;
                echo '<a href="?' . http_build_query(array_merge($_GET, ['page' => $prevPage])) . '">‹ 이전</a>';
            } else {
                echo '<span class="disabled">‹ 이전</span>';
            }
            
            // 페이지 번호들
            $startPage = max(1, $page - 5);
            $endPage = min($totalPages, $page + 5);
            
            if ($startPage > 1) {
                echo '<a href="?' . http_build_query(array_merge($_GET, ['page' => 1])) . '">1</a>';
                if ($startPage > 2) {
                    echo '<span>...</span>';
                }
            }
            
            for ($i = $startPage; $i <= $endPage; $i++) {
                if ($i == $page) {
                    echo '<span class="current">' . $i . '</span>';
                } else {
                    echo '<a href="?' . http_build_query(array_merge($_GET, ['page' => $i])) . '">' . $i . '</a>';
                }
            }
            
            if ($endPage < $totalPages) {
                if ($endPage < $totalPages - 1) {
                    echo '<span>...</span>';
                }
                echo '<a href="?' . http_build_query(array_merge($_GET, ['page' => $totalPages])) . '">' . $totalPages . '</a>';
            }
            
            // 다음 페이지
            if ($page < $totalPages) {
                $nextPage = $page + 1;
                echo '<a href="?' . http_build_query(array_merge($_GET, ['page' => $nextPage])) . '">다음 ›</a>';
            } else {
                echo '<span class="disabled">다음 ›</span>';
            }
            
            echo '</div>';
        }
    }
    
} catch (Exception $e) {
    echo '<div style="color: red; padding: 20px; background-color: #ffe6e6; border: 1px solid #ff9999; border-radius: 5px;">';
    echo '<h3>❌ 오류 발생</h3>';
    echo '<p>' . htmlspecialchars($e->getMessage()) . '</p>';
    echo '</div>';
}
?>

    </div>
</body>
</html>
