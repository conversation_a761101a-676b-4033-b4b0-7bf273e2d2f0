<!DOCTYPE html>
<html lang="ko">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>데이터베이스 설정 - 재고관리시스템</title>
</head>
<body>
    <?php include '../components/navbar.php'; ?>

    <div class="page-content" style="max-width: 1000px; margin: 20px auto; padding: 20px; background: white; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">

<?php
/**
 * 데이터베이스 테이블 설정 스크립트
 * 재고 관리 시스템에 필요한 테이블들을 생성하고 컬럼을 추가합니다.
 */

require_once '../conf/Database.php';

try {
    $db = new Database();

    echo "<h2>📊 데이터베이스 테이블 설정</h2>";
    
    // 1. product_master 테이블에 current_amount 컬럼 추가
    echo "<h3>1. product_master 테이블 업데이트</h3>";

    // 컬럼 존재 여부 확인 함수
    function columnExists($db, $table, $column) {
        try {
            $result = $db->select("SHOW COLUMNS FROM {$table} LIKE '{$column}'");
            return !empty($result);
        } catch (Exception $e) {
            return false;
        }
    }

    // current_amount 컬럼 추가
    if (!columnExists($db, 'product_master', 'current_amount')) {
        try {
            $db->connect()->exec("ALTER TABLE product_master ADD COLUMN current_amount INT DEFAULT 0 COMMENT '현재 재고량'");
            echo "✅ product_master 테이블에 current_amount 컬럼 추가 완료<br>";
        } catch (Exception $e) {
            echo "⚠️ current_amount 컬럼 추가 실패: " . $e->getMessage() . "<br>";
        }
    } else {
        echo "ℹ️ current_amount 컬럼이 이미 존재합니다<br>";
    }

    // created_at 컬럼 추가
    if (!columnExists($db, 'product_master', 'created_at')) {
        try {
            $db->connect()->exec("ALTER TABLE product_master ADD COLUMN created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '생성일시'");
            echo "✅ product_master 테이블에 created_at 컬럼 추가 완료<br>";
        } catch (Exception $e) {
            echo "⚠️ created_at 컬럼 추가 실패: " . $e->getMessage() . "<br>";
        }
    } else {
        echo "ℹ️ created_at 컬럼이 이미 존재합니다<br>";
    }

    // updated_at 컬럼 추가
    if (!columnExists($db, 'product_master', 'updated_at')) {
        try {
            $db->connect()->exec("ALTER TABLE product_master ADD COLUMN updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '수정일시'");
            echo "✅ product_master 테이블에 updated_at 컬럼 추가 완료<br>";
        } catch (Exception $e) {
            echo "⚠️ updated_at 컬럼 추가 실패: " . $e->getMessage() . "<br>";
        }
    } else {
        echo "ℹ️ updated_at 컬럼이 이미 존재합니다<br>";
    }
    
    // 2. product_mapping 테이블 생성 (orderForm과 product_master 연결)
    echo "<h3>2. product_mapping 테이블 생성</h3>";
    
    $createMappingTableSql = "
        CREATE TABLE IF NOT EXISTS product_mapping (
            id INT AUTO_INCREMENT PRIMARY KEY,
            product_name_sur VARCHAR(255) NOT NULL COMMENT '상품명',
            product_name_option VARCHAR(255) NOT NULL COMMENT '옵션명',
            option_no VARCHAR(100) NOT NULL COMMENT '연결된 옵션번호',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '생성일시',
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '수정일시',
            UNIQUE KEY unique_product_option (product_name_sur, product_name_option),
            INDEX idx_option_no (option_no),
            INDEX idx_product_names (product_name_sur, product_name_option)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='상품 매핑 테이블'
    ";
    
    $db->connect()->exec($createMappingTableSql);
    echo "✅ product_mapping 테이블 생성 완료<br>";
    
    // 3. orderForm 테이블에 mapping_status 컬럼 추가
    echo "<h3>3. orderForm 테이블 업데이트</h3>";

    // mapping_status 컬럼 추가
    if (!columnExists($db, 'orderForm', 'mapping_status')) {
        try {
            $db->connect()->exec("ALTER TABLE orderForm ADD COLUMN mapping_status ENUM('mapped', 'unmapped') DEFAULT 'unmapped' COMMENT '매핑 상태'");
            echo "✅ orderForm 테이블에 mapping_status 컬럼 추가 완료<br>";
        } catch (Exception $e) {
            echo "⚠️ mapping_status 컬럼 추가 실패: " . $e->getMessage() . "<br>";
        }
    } else {
        echo "ℹ️ mapping_status 컬럼이 이미 존재합니다<br>";
    }

    // mapped_option_no 컬럼 추가
    if (!columnExists($db, 'orderForm', 'mapped_option_no')) {
        try {
            $db->connect()->exec("ALTER TABLE orderForm ADD COLUMN mapped_option_no VARCHAR(100) NULL COMMENT '매핑된 옵션번호'");
            echo "✅ orderForm 테이블에 mapped_option_no 컬럼 추가 완료<br>";
        } catch (Exception $e) {
            echo "⚠️ mapped_option_no 컬럼 추가 실패: " . $e->getMessage() . "<br>";
        }
    } else {
        echo "ℹ️ mapped_option_no 컬럼이 이미 존재합니다<br>";
    }

    // 인덱스 추가 (존재하지 않을 경우에만)
    try {
        $db->connect()->exec("CREATE INDEX idx_mapping_status ON orderForm (mapping_status)");
        echo "✅ mapping_status 인덱스 추가 완료<br>";
    } catch (Exception $e) {
        if (strpos($e->getMessage(), 'Duplicate key name') === false) {
            echo "⚠️ mapping_status 인덱스 추가 실패: " . $e->getMessage() . "<br>";
        } else {
            echo "ℹ️ mapping_status 인덱스가 이미 존재합니다<br>";
        }
    }

    try {
        $db->connect()->exec("CREATE INDEX idx_mapped_option_no ON orderForm (mapped_option_no)");
        echo "✅ mapped_option_no 인덱스 추가 완료<br>";
    } catch (Exception $e) {
        if (strpos($e->getMessage(), 'Duplicate key name') === false) {
            echo "⚠️ mapped_option_no 인덱스 추가 실패: " . $e->getMessage() . "<br>";
        } else {
            echo "ℹ️ mapped_option_no 인덱스가 이미 존재합니다<br>";
        }
    }
    
    // 4. 기존 데이터의 current_amount 초기화 (initial_amount와 동일하게 설정)
    echo "<h3>4. 기존 데이터 초기화</h3>";

    // current_amount 컬럼이 존재하는 경우에만 초기화
    if (columnExists($db, 'product_master', 'current_amount')) {
        try {
            $updateCurrentAmountSql = "
                UPDATE product_master
                SET current_amount = initial_amount
                WHERE current_amount = 0 OR current_amount IS NULL
            ";

            $updatedRows = $db->connect()->exec($updateCurrentAmountSql);
            echo "✅ {$updatedRows}개 상품의 current_amount 초기화 완료<br>";
        } catch (Exception $e) {
            echo "⚠️ current_amount 초기화 실패: " . $e->getMessage() . "<br>";
        }
    } else {
        echo "⚠️ current_amount 컬럼이 존재하지 않아 초기화를 건너뜁니다<br>";
    }
    
    // 5. 테이블 상태 확인
    echo "<h3>5. 테이블 상태 확인</h3>";
    
    // product_master 테이블 정보
    $productMasterInfo = $db->select("DESCRIBE product_master");
    echo "<h4>📋 product_master 테이블 구조:</h4>";
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>컬럼명</th><th>타입</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    foreach ($productMasterInfo as $column) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($column['Field']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Type']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Null']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Key']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Default']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Extra']) . "</td>";
        echo "</tr>";
    }
    echo "</table><br>";
    
    // product_mapping 테이블 정보
    $mappingInfo = $db->select("DESCRIBE product_mapping");
    echo "<h4>📋 product_mapping 테이블 구조:</h4>";
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>컬럼명</th><th>타입</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    foreach ($mappingInfo as $column) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($column['Field']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Type']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Null']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Key']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Default']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Extra']) . "</td>";
        echo "</tr>";
    }
    echo "</table><br>";
    
    // orderForm 테이블 정보
    $orderFormInfo = $db->select("DESCRIBE orderForm");
    echo "<h4>📋 orderForm 테이블 구조:</h4>";
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>컬럼명</th><th>타입</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    foreach ($orderFormInfo as $column) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($column['Field']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Type']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Null']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Key']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Default']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Extra']) . "</td>";
        echo "</tr>";
    }
    echo "</table><br>";
    
    echo "<h3>🎉 데이터베이스 설정 완료!</h3>";
    echo "<p>이제 재고 관리 시스템을 사용할 수 있습니다.</p>";
    
} catch (Exception $e) {
    echo "<div style='color: red; padding: 20px; background-color: #ffe6e6; border: 1px solid #ff9999; border-radius: 5px;'>";
    echo "<h3>❌ 오류 발생</h3>";
    echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 1000px;
    margin: 0 auto;
    padding: 20px;
    background-color: #f5f5f5;
}
table {
    background: white;
    border-collapse: collapse;
    width: 100%;
    margin-bottom: 20px;
}
th, td {
    padding: 8px;
    text-align: left;
    border: 1px solid #ddd;
}
th {
    background-color: #007cba;
    color: white;
}
h2, h3, h4 {
    color: #333;
}
</style>

    </div>
</body>
</html>
