<!DOCTYPE html>
<html lang="ko">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>상품 관리 - 재고관리시스템</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 3px solid #007cba;
            padding-bottom: 10px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }
        .form-control {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            box-sizing: border-box;
            font-size: 16px;
        }
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin: 5px;
            font-size: 16px;
        }
        .btn-primary {
            background-color: #007cba;
            color: white;
        }
        .btn-secondary {
            background-color: #6c757d;
            color: white;
        }
        .btn:hover {
            opacity: 0.8;
        }
        .alert {
            padding: 15px;
            margin-bottom: 20px;
            border: 1px solid transparent;
            border-radius: 4px;
        }
        .alert-success {
            color: #155724;
            background-color: #d4edda;
            border-color: #c3e6cb;
        }
        .alert-danger {
            color: #721c24;
            background-color: #f8d7da;
            border-color: #f5c6cb;
        }
        .actions {
            text-align: center;
            margin-top: 30px;
        }
    </style>
</head>
<body>
    <?php include '../components/navbar.php'; ?>

    <div class="container page-content">
        <?php
        require_once '../conf/Database.php';

        $action = $_GET['action'] ?? '';
        $productId = $_GET['id'] ?? '';
        $db = new Database();

        // 메시지 표시 함수
        function showMessage($message, $type = 'info') {
            echo "<div class='alert alert-{$type}'>{$message}</div>";
        }

        // POST 요청 처리
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            try {
                if (isset($_POST['add_product'])) {
                    // 상품 추가
                    $data = [
                        'option_no' => $_POST['option_no'],
                        'product_name' => $_POST['product_name'],
                        'initial_amount' => intval($_POST['initial_amount'])
                    ];
                    
                    $db->insert('product_master', $data);
                    showMessage('✅ 상품이 성공적으로 추가되었습니다.', 'success');
                    
                } elseif (isset($_POST['update_product'])) {
                    // 상품 수정
                    $data = [
                        'product_name' => $_POST['product_name'],
                        'initial_amount' => intval($_POST['initial_amount'])
                    ];
                    
                    $db->update('product_master', $data, 'option_no = :option_no', ['option_no' => $_POST['option_no']]);
                    showMessage('✅ 상품이 성공적으로 수정되었습니다.', 'success');
                    
                } elseif (isset($_POST['delete_product'])) {
                    // 상품 삭제
                    $optionNo = $_POST['option_no'];
                    
                    // 연결된 매핑이 있는지 확인
                    $mappingCount = $db->selectOne("SELECT COUNT(*) as count FROM product_mapping WHERE option_no = :option_no", ['option_no' => $optionNo])['count'];
                    
                    if ($mappingCount > 0) {
                        showMessage('❌ 이 상품은 주문 매핑과 연결되어 있어 삭제할 수 없습니다. 먼저 매핑을 삭제해주세요.', 'danger');
                    } else {
                        $db->delete('product_master', 'option_no = :option_no', ['option_no' => $optionNo]);
                        showMessage('✅ 상품이 성공적으로 삭제되었습니다.', 'success');
                        echo '<script>setTimeout(function(){ window.location.href = "view_product_master.php"; }, 2000);</script>';
                    }
                }
                
            } catch (Exception $e) {
                if (strpos($e->getMessage(), 'Duplicate entry') !== false) {
                    showMessage('❌ 이미 존재하는 옵션번호입니다.', 'danger');
                } else {
                    showMessage('❌ 오류 발생: ' . $e->getMessage(), 'danger');
                }
            }
        }

        // 기존 상품 정보 조회 (수정/삭제용)
        $product = null;
        if (($action === 'edit' || $action === 'delete') && $productId) {
            $product = $db->selectOne("SELECT * FROM product_master WHERE option_no = :option_no", ['option_no' => $productId]);
            if (!$product) {
                showMessage('❌ 상품을 찾을 수 없습니다.', 'danger');
                echo '<div class="actions"><a href="view_product_master.php" class="btn btn-secondary">목록으로 돌아가기</a></div>';
                echo '</div></body></html>';
                exit;
            }
        }

        // 페이지 제목 설정
        $pageTitle = '';
        switch ($action) {
            case 'add':
                $pageTitle = '➕ 새 상품 추가';
                break;
            case 'edit':
                $pageTitle = '✏️ 상품 수정';
                break;
            case 'delete':
                $pageTitle = '🗑️ 상품 삭제';
                break;
            default:
                $pageTitle = '📦 상품 관리';
        }

        echo "<h1>{$pageTitle}</h1>";

        if ($action === 'add' || $action === 'edit') {
            // 추가/수정 폼
            ?>
            <form method="POST">
                <div class="form-group">
                    <label for="option_no">옵션번호 *</label>
                    <input type="text" 
                           id="option_no" 
                           name="option_no" 
                           class="form-control" 
                           value="<?php echo htmlspecialchars($product['option_no'] ?? ''); ?>"
                           <?php echo ($action === 'edit') ? 'readonly' : 'required'; ?>>
                    <?php if ($action === 'edit'): ?>
                        <small style="color: #6c757d;">옵션번호는 수정할 수 없습니다.</small>
                    <?php endif; ?>
                </div>
                
                <div class="form-group">
                    <label for="product_name">상품명 *</label>
                    <input type="text" 
                           id="product_name" 
                           name="product_name" 
                           class="form-control" 
                           value="<?php echo htmlspecialchars($product['product_name'] ?? ''); ?>"
                           required>
                </div>
                
                <div class="form-group">
                    <label for="initial_amount">초기수량 *</label>
                    <input type="number"
                           id="initial_amount"
                           name="initial_amount"
                           class="form-control"
                           value="<?php echo $product['initial_amount'] ?? 0; ?>"
                           required>
                    <small style="color: #6c757d;">음수 값도 입력 가능합니다.</small>
                </div>
                
                <div class="actions">
                    <?php if ($action === 'add'): ?>
                        <button type="submit" name="add_product" class="btn btn-primary">상품 추가</button>
                    <?php else: ?>
                        <button type="submit" name="update_product" class="btn btn-primary">상품 수정</button>
                    <?php endif; ?>
                    <a href="view_product_master.php" class="btn btn-secondary">취소</a>
                </div>
            </form>
            <?php
           

        } elseif ($action === 'delete' && $product) {
            // 삭제 확인
            ?>
            <div style="background-color: #f8d7da; border: 1px solid #f5c6cb; padding: 20px; border-radius: 5px; margin-bottom: 20px;">
                <h3 style="color: #721c24; margin-top: 0;">⚠️ 삭제 확인</h3>
                <p style="color: #721c24;">다음 상품을 정말 삭제하시겠습니까?</p>
                
                <table style="width: 100%; border-collapse: collapse; margin: 15px 0;">
                    <tr style="background-color: #f8f9fa;">
                        <th style="padding: 10px; border: 1px solid #ddd; text-align: left;">옵션번호</th>
                        <td style="padding: 10px; border: 1px solid #ddd;"><?php echo htmlspecialchars($product['option_no']); ?></td>
                    </tr>
                    <tr>
                        <th style="padding: 10px; border: 1px solid #ddd; text-align: left;">상품명</th>
                        <td style="padding: 10px; border: 1px solid #ddd;"><?php echo htmlspecialchars($product['product_name']); ?></td>
                    </tr>
                    <tr style="background-color: #f8f9fa;">
                        <th style="padding: 10px; border: 1px solid #ddd; text-align: left;">초기수량</th>
                        <td style="padding: 10px; border: 1px solid #ddd;"><?php echo number_format($product['initial_amount']); ?></td>
                    </tr>
                </table>
                
                <p style="color: #721c24;"><strong>주의:</strong> 삭제된 상품은 복구할 수 없습니다.</p>
            </div>
            
            <form method="POST">
                <input type="hidden" name="option_no" value="<?php echo htmlspecialchars($product['option_no']); ?>">
                <div class="actions">
                    <button type="submit" name="delete_product" class="btn" style="background-color: #dc3545; color: white;">삭제 확인</button>
                    <a href="view_product_master.php" class="btn btn-secondary">취소</a>
                </div>
            </form>
            <?php
            
        } else {
            // 기본 페이지
            echo '<div style="text-align: center; padding: 50px;">';
            echo '<p style="font-size: 18px; color: #6c757d;">상품을 관리할 수 있습니다.</p>';
            echo '<div class="actions">';
            echo '<a href="?action=add" class="btn btn-primary">➕ 새 상품 추가</a>';
            echo '<a href="view_product_master.php" class="btn btn-secondary">📦 상품 목록 보기</a>';
            echo '</div>';
            echo '</div>';
        }
        ?>
    </div>
</body>
</html>
