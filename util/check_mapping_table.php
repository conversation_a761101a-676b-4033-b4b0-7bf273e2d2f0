<!DOCTYPE html>
<html lang="ko">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>매핑 테이블 확인 - 재고관리시스템</title>
</head>
<body>
    <?php include '../components/navbar.php'; ?>
    
    <div class="page-content" style="max-width: 1000px; margin: 20px auto; padding: 20px; background: white; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">

<?php
require_once '../conf/Database.php';

try {
    $db = new Database();
    
    echo "<h2>🔍 product_mapping 테이블 구조 확인</h2>";
    
    // 테이블 구조 확인
    try {
        $columns = $db->select("DESCRIBE product_mapping");
        echo "<h3>📋 현재 테이블 구조</h3>";
        echo "<table border='1' cellpadding='5' style='border-collapse: collapse; width: 100%; margin-bottom: 20px;'>";
        echo "<tr style='background-color: #007cba; color: white;'><th>컬럼명</th><th>타입</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
        foreach ($columns as $column) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($column['Field'] ?? '') . "</td>";
            echo "<td>" . htmlspecialchars($column['Type'] ?? '') . "</td>";
            echo "<td>" . htmlspecialchars($column['Null'] ?? '') . "</td>";
            echo "<td>" . htmlspecialchars($column['Key'] ?? '') . "</td>";
            echo "<td>" . htmlspecialchars($column['Default'] ?? '') . "</td>";
            echo "<td>" . htmlspecialchars($column['Extra'] ?? '') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // 필요한 컬럼들 확인
        $requiredColumns = ['mapped_option_no', 'mapped_product_name'];
        $existingColumns = array_column($columns, 'Field');
        
        echo "<h3>🔍 필수 컬럼 확인</h3>";
        foreach ($requiredColumns as $col) {
            if (in_array($col, $existingColumns)) {
                echo "<p>✅ {$col} - 존재함</p>";
            } else {
                echo "<p>❌ {$col} - 없음 (추가 필요)</p>";
            }
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>테이블 구조 조회 실패: " . $e->getMessage() . "</p>";
    }
    
    // 샘플 데이터 확인
    echo "<h3>📊 샘플 데이터</h3>";
    try {
        $sampleData = $db->select("SELECT * FROM product_mapping LIMIT 5");
        if (!empty($sampleData)) {
            echo "<table border='1' cellpadding='5' style='border-collapse: collapse; width: 100%; margin-bottom: 20px;'>";
            echo "<tr style='background-color: #28a745; color: white;'>";
            foreach (array_keys($sampleData[0]) as $key) {
                echo "<th>" . htmlspecialchars($key) . "</th>";
            }
            echo "</tr>";
            foreach ($sampleData as $data) {
                echo "<tr>";
                foreach ($data as $value) {
                    echo "<td>" . htmlspecialchars($value ?? '') . "</td>";
                }
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p>샘플 데이터가 없습니다.</p>";
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>샘플 데이터 조회 실패: " . $e->getMessage() . "</p>";
    }
    
    // 컬럼 추가 버튼
    echo "<h3>🔧 테이블 수정</h3>";
    
    if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['add_columns'])) {
        try {
            echo "<h4>컬럼 추가 중...</h4>";
            
            // mapped_option_no 컬럼 추가
            try {
                $db->connect()->exec("ALTER TABLE product_mapping ADD COLUMN mapped_option_no VARCHAR(255) DEFAULT NULL COMMENT '연결된 옵션번호'");
                echo "<p>✅ mapped_option_no 컬럼 추가 완료</p>";
            } catch (Exception $e) {
                if (strpos($e->getMessage(), 'Duplicate column name') !== false) {
                    echo "<p>⚠️ mapped_option_no 컬럼이 이미 존재합니다</p>";
                } else {
                    echo "<p style='color: red;'>❌ mapped_option_no 컬럼 추가 실패: " . $e->getMessage() . "</p>";
                }
            }
            
            // mapped_product_name 컬럼 추가
            try {
                $db->connect()->exec("ALTER TABLE product_mapping ADD COLUMN mapped_product_name VARCHAR(255) DEFAULT NULL COMMENT '연결된 상품명'");
                echo "<p>✅ mapped_product_name 컬럼 추가 완료</p>";
            } catch (Exception $e) {
                if (strpos($e->getMessage(), 'Duplicate column name') !== false) {
                    echo "<p>⚠️ mapped_product_name 컬럼이 이미 존재합니다</p>";
                } else {
                    echo "<p style='color: red;'>❌ mapped_product_name 컬럼 추가 실패: " . $e->getMessage() . "</p>";
                }
            }
            
            echo "<p><strong>페이지를 새로고침하여 변경사항을 확인하세요.</strong></p>";
            
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ 컬럼 추가 중 오류: " . $e->getMessage() . "</p>";
        }
    }
    
    echo '<form method="POST">';
    echo '<button type="submit" name="add_columns" style="padding: 10px 20px; background-color: #28a745; color: white; border: none; border-radius: 5px; cursor: pointer;">필수 컬럼 추가</button>';
    echo '</form>';
    
    echo '<div style="text-align: center; margin-top: 20px;">';
    echo '<a href="../page/compare_product_stock.php" style="padding: 10px 20px; background-color: #007cba; color: white; text-decoration: none; border-radius: 5px; margin: 5px;">🔗 상품 연결 페이지로 이동</a>';
    echo '</div>';
    
} catch (Exception $e) {
    echo "<div style='color: red; padding: 20px; background-color: #ffe6e6; border: 1px solid #ff9999; border-radius: 5px;'>";
    echo "<h3>❌ 오류 발생</h3>";
    echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}
?>

<style>
body {
    font-family: Arial, sans-serif;
    background-color: #f5f5f5;
}
table {
    background: white;
    border-collapse: collapse;
    width: 100%;
    margin-bottom: 20px;
}
th, td {
    padding: 8px;
    text-align: left;
    border: 1px solid #ddd;
}
h2, h3 {
    color: #333;
}
</style>

    </div>
</body>
</html>
