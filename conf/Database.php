<?php

class Database {
    private $host;
    private $db_name;
    private $username;
    private $password;
    private $port;
    private $charset;
    private $pdo;
    
    public function __construct() {
        // db.php 설정 파일 로드
        require_once __DIR__ . '/db_info.php';
        
        $this->host = $server;
        $this->db_name = $db;
        $this->username = $username;
        $this->password = $password;
        $this->port = $port;
        $this->charset = $charset;
    }
    
    /**
     * 데이터베이스 연결
     */
    public function connect() {
        if ($this->pdo === null) {
            try {
                // charset을 DSN에서 제거하고 옵션으로만 설정
                $dsn = "mysql:host={$this->host};port={$this->port};dbname={$this->db_name}";

                $options = [
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                    PDO::ATTR_EMULATE_PREPARES => false,
                ];

                $this->pdo = new PDO($dsn, $this->username, $this->password, $options);

                // 연결 후 문자셋 설정 (호환성을 위해 여러 방법 시도)
                try {
                    $this->pdo->exec("SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci");
                } catch (PDOException $e) {
                    // utf8mb4가 지원되지 않는 경우 utf8 사용
                    try {
                        $this->pdo->exec("SET NAMES utf8 COLLATE utf8_unicode_ci");
                    } catch (PDOException $e2) {
                        // 기본 utf8 설정
                        $this->pdo->exec("SET NAMES utf8");
                    }
                }
                
            } catch (PDOException $e) {
                throw new Exception("데이터베이스 연결 실패: " . $e->getMessage());
            }
        }
        
        return $this->pdo;
    }
    
    /**
     * SELECT 쿼리 실행 (여러 행 반환)
     */
    public function select($sql, $params = []) {
        try {
            $stmt = $this->connect()->prepare($sql);
            $stmt->execute($params);
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            throw new Exception("SELECT 쿼리 실행 실패: " . $e->getMessage());
        }
    }
    
    /**
     * SELECT 쿼리 실행 (단일 행 반환)
     */
    public function selectOne($sql, $params = []) {
        try {
            $stmt = $this->connect()->prepare($sql);
            $stmt->execute($params);
            return $stmt->fetch();
        } catch (PDOException $e) {
            throw new Exception("SELECT 쿼리 실행 실패: " . $e->getMessage());
        }
    }
    
    /**
     * INSERT 쿼리 실행
     */
    public function insert($table, $data) {
        try {
            $columns = implode(',', array_keys($data));
            $placeholders = ':' . implode(', :', array_keys($data));
            
            $sql = "INSERT INTO {$table} ({$columns}) VALUES ({$placeholders})";
            
            $stmt = $this->connect()->prepare($sql);
            $stmt->execute($data);
            
            return $this->pdo->lastInsertId();
        } catch (PDOException $e) {
            throw new Exception("INSERT 쿼리 실행 실패: " . $e->getMessage());
        }
    }
    
    /**
     * UPDATE 쿼리 실행
     */
    public function update($table, $data, $where, $whereParams = []) {
        try {
            $setClause = [];
            foreach (array_keys($data) as $key) {
                $setClause[] = "{$key} = :{$key}";
            }
            $setClause = implode(', ', $setClause);
            
            $sql = "UPDATE {$table} SET {$setClause} WHERE {$where}";
            
            $params = array_merge($data, $whereParams);
            $stmt = $this->connect()->prepare($sql);
            $stmt->execute($params);
            
            return $stmt->rowCount();
        } catch (PDOException $e) {
            throw new Exception("UPDATE 쿼리 실행 실패: " . $e->getMessage());
        }
    }
    
    /**
     * DELETE 쿼리 실행
     */
    public function delete($table, $where, $params = []) {
        try {
            $sql = "DELETE FROM {$table} WHERE {$where}";
            
            $stmt = $this->connect()->prepare($sql);
            $stmt->execute($params);
            
            return $stmt->rowCount();
        } catch (PDOException $e) {
            throw new Exception("DELETE 쿼리 실행 실패: " . $e->getMessage());
        }
    }
    
    /**
     * 트랜잭션 시작
     */
    public function beginTransaction() {
        $pdo = $this->connect();
        if ($pdo && !$pdo->inTransaction()) {
            return $pdo->beginTransaction();
        }
        return false;
    }

    /**
     * 트랜잭션 커밋
     */
    public function commit() {
        if ($this->pdo && $this->pdo->inTransaction()) {
            return $this->pdo->commit();
        }
        return false;
    }
    
    /**
     * 트랜잭션 롤백
     */
    public function rollback() {
        if ($this->pdo && $this->pdo->inTransaction()) {
            return $this->pdo->rollback();
        }
        return false;
    }
    
    /**
     * 연결 종료
     */
    public function close() {
        $this->pdo = null;
    }
}
