<?php
// API 요청인 경우 HTML 출력 방지
if (isset($_GET['action']) && $_GET['action'] === 'list_files') {
    // 출력 버퍼 시작
    ob_start();
}
?>
<!DOCTYPE html>
<html lang="ko">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>주문서 업로드 - 재고관리시스템</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 3px solid #007cba;
            padding-bottom: 10px;
        }
        .upload-area {
            border: 3px dashed #007cba;
            border-radius: 10px;
            padding: 50px;
            text-align: center;
            margin-bottom: 20px;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        .upload-area:hover, .upload-area.dragover {
            background-color: #e3f2fd;
            border-color: #0056b3;
        }
        .upload-btn {
            background-color: #007cba;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin-top: 10px;
        }
        .upload-btn:hover {
            background-color: #005a8b;
        }
        .file-input {
            display: none;
        }
        .file-info {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            display: none;
        }
        .process-btn {
            background-color: #28a745;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 18px;
            width: 100%;
            margin-bottom: 20px;
        }
        .process-btn:hover {
            background-color: #218838;
        }
        .process-btn:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .result {
            margin-top: 20px;
            padding: 20px;
            border-radius: 5px;
        }
        .result.success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .result.error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info-box {
            background-color: #e9ecef;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .info-box h3 {
            margin-top: 0;
            color: #495057;
        }
        .delete-section {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 20px;
            border-radius: 5px;
            margin-top: 20px;
        }
        .delete-section h3 {
            color: #856404;
            margin-top: 0;
        }
        .delete-btn {
            background-color: #dc3545;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin-left: 10px;
        }
        .delete-btn:hover {
            background-color: #c82333;
        }
        .file-list {
            max-height: 500px;
            overflow-y: auto;
            border: 1px solid #ddd;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 10px;
            background-color: #ffffff;
        }
        .file-item {
            padding: 15px;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            background-color: #f8f9fa;
            margin-bottom: 10px;
            border-radius: 5px;
            border: 1px solid #dee2e6;
        }
        .file-item:last-child {
            border-bottom: 1px solid #dee2e6;
        }
        .file-item:hover {
            background-color: #e9ecef;
        }
    </style>
</head>
<body>
    <?php include '../components/navbar.php'; ?>

    <div class="container page-content">
        <h1>📋 주문서 엑셀 업로드</h1>
        
        <div class="info-box">
            <h3>📝 엑셀 파일 형식 안내</h3>
            <ul>
                <li><strong>B2:</strong> '상품명' 컬럼</li>
                <li><strong>C2:</strong> '옵션명' 컬럼</li>
                <li><strong>D2:</strong> '수량' 컬럼</li>
                <li><strong>파일명:</strong> '파일명_YYYYMMDDHHMMSS.xlsx' 형식</li>
            </ul>
            <p><strong>주의:</strong> 파일명의 마지막 언더스코어(_) 이후 14자리 숫자에서 날짜/시간 정보를 추출합니다.</p>
            <p><strong>예시:</strong> 발주서_20250713195814.xlsx, 주문서_20250713195814.xlsx 등</p>
        </div>

        <form id="uploadForm" enctype="multipart/form-data">
            <div class="upload-area" id="uploadArea">
                <p>📁 주문서 엑셀 파일을 여기에 드래그하거나 클릭하여 선택하세요</p>
                <p>지원 형식: .xlsx, .xls</p>
                <button type="button" class="upload-btn" onclick="document.getElementById('fileInput').click()">
                    파일 선택
                </button>
                <input type="file" id="fileInput" class="file-input" accept=".xlsx,.xls" />
            </div>
            
            <div class="file-info" id="fileInfo">
                <h4>선택된 파일:</h4>
                <p id="fileName"></p>
                <p id="fileSize"></p>
            </div>
            
            <button type="submit" class="process-btn" id="processBtn">
                🚀 주문서 데이터 업로드
            </button>
        </form>

        <div id="result"></div>

        <!-- 파일별 삭제 기능 -->
        <div class="delete-section">
            <h3>🗑️ 업로드된 엑셀 파일별 데이터 삭제</h3>
            <p>업로드된 엑셀 파일별로 해당 파일의 모든 주문 데이터를 삭제할 수 있습니다.</p>
            <p><strong>주의:</strong> 삭제된 데이터는 복구할 수 없습니다.</p>

            <div id="fileListContainer">
                <button type="button" onclick="loadFileList()" class="upload-btn">📋 업로드된 파일 목록 새로고침</button>
                <div id="fileList" class="file-list"></div>
            </div>
        </div>
    </div>

    <script>
        const uploadArea = document.getElementById('uploadArea');
        const fileInput = document.getElementById('fileInput');
        const fileInfo = document.getElementById('fileInfo');
        const processBtn = document.getElementById('processBtn');
        const uploadForm = document.getElementById('uploadForm');

        // 드래그앤드롭 이벤트
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });

        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });

        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                handleFile(files[0]);
            }
        });

        // 파일 선택 이벤트
        fileInput.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                handleFile(e.target.files[0]);
            }
        });

        // 파일 처리 함수
        function handleFile(file) {
            const fileName = file.name;
            const fileSize = (file.size / 1024 / 1024).toFixed(2) + ' MB';

            document.getElementById('fileName').textContent = fileName;
            document.getElementById('fileSize').textContent = fileSize;
            fileInfo.style.display = 'block';

            // 클라이언트 사이드 검증 제거 - 서버에서만 검증
            console.log('선택된 파일:', fileName);
        }

        // 폼 제출
        uploadForm.addEventListener('submit', async (e) => {
            e.preventDefault();
            
            if (!fileInput.files[0]) {
                alert('파일을 선택해주세요.');
                return;
            }

            const formData = new FormData();
            formData.append('excel_file', fileInput.files[0]);

            processBtn.textContent = '처리 중...';
            processBtn.disabled = true;

            try {
                const response = await fetch('upload_orderform_excel.php', {
                    method: 'POST',
                    body: formData
                });

                const result = await response.text();
                document.getElementById('result').innerHTML = result;
                
                // 성공 시 파일 목록 새로고침
                if (result.includes('success')) {
                    setTimeout(() => {
                        loadFileList();
                    }, 1000);
                }
                
            } catch (error) {
                document.getElementById('result').innerHTML = 
                    '<div class="result error">업로드 중 오류가 발생했습니다: ' + error.message + '</div>';
            } finally {
                processBtn.textContent = '🚀 주문서 데이터 업로드';
                processBtn.disabled = false;
            }
        });

        // 파일 목록 로드
        async function loadFileList() {
            try {
                console.log('파일 목록 로드 시작...');
                const response = await fetch('upload_orderform_excel.php?action=list_files');

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const text = await response.text();
                console.log('서버 원본 응답:', text);

                let result;
                try {
                    result = JSON.parse(text);
                } catch (parseError) {
                    console.error('JSON 파싱 실패:', parseError);
                    console.error('원본 응답:', text);
                    throw new Error(`JSON 파싱 실패: ${parseError.message}\n\n원본 응답 (처음 500자):\n${text.substring(0, 500)}`);
                }

                console.log('파싱된 응답:', result);

                if (result.error) {
                    throw new Error(result.error);
                }

                const fileListDiv = document.getElementById('fileList');
                console.log('파일 데이터:', result.files);
                console.log('파일 개수:', result.files ? result.files.length : 0);

                if (result.files && result.files.length > 0) {
                    let fileListHTML = '<div style="margin-bottom: 15px; padding: 10px; background-color: #e9ecef; border-radius: 5px;">';
                    fileListHTML += `<strong>📊 총 ${result.files.length}개의 엑셀 파일이 업로드되어 있습니다.</strong>`;
                    fileListHTML += '</div>';

                    fileListHTML += result.files.map((file, index) => {
                        const mappingStatus = file.mapped_count > 0 ?
                            `🔗 연결: ${file.mapped_count}개` : '';
                        const unmappingStatus = file.unmapped_count > 0 ?
                            `❓ 미연결: ${file.unmapped_count}개` : '';
                        const statusText = [mappingStatus, unmappingStatus].filter(s => s).join(', ');

                        return `<div class="file-item">
                            <div style="flex: 1;">
                                <div style="font-weight: bold; margin-bottom: 8px; color: #007cba;">
                                    📄 ${index + 1}. ${file.filename}
                                </div>
                                <div style="font-size: 0.9em; color: #495057; line-height: 1.4;">
                                    <div>📅 업로드 일시: ${file.date_join}</div>
                                    <div>📦 주문 항목 수: <strong>${file.count}개</strong></div>
                                    <div>📊 총 주문 수량: <strong>${parseInt(file.total_amount).toLocaleString()}개</strong></div>
                                    ${statusText ? `<div style="margin-top: 5px; padding: 5px; background-color: #f8f9fa; border-radius: 3px;">${statusText}</div>` : ''}
                                </div>
                            </div>
                            <div style="display: flex; flex-direction: column; align-items: center;">
                                <button class="delete-btn" onclick="deleteFileData('${file.filename}')"
                                        style="margin-bottom: 5px;">
                                    🗑️ 삭제
                                </button>
                                <small style="color: #6c757d; text-align: center;">
                                    ${file.count}개 항목<br>모두 삭제
                                </small>
                            </div>
                        </div>`;
                    }).join('');

                    fileListDiv.innerHTML = fileListHTML;
                } else {
                    fileListDiv.innerHTML = `
                        <div style="text-align: center; color: #6c757d; padding: 40px; background-color: #f8f9fa; border-radius: 5px; border: 2px dashed #dee2e6;">
                            <div style="font-size: 48px; margin-bottom: 15px;">📭</div>
                            <h4>업로드된 엑셀 파일이 없습니다</h4>
                            <p>주문서 엑셀 파일을 업로드하면 여기에 목록이 표시됩니다.</p>
                        </div>
                    `;
                }
            } catch (error) {
                console.error('파일 목록 로드 실패:', error);
                const fileListDiv = document.getElementById('fileList');
                fileListDiv.innerHTML = `
                    <div style="text-align: center; color: #dc3545; padding: 20px; background-color: #f8d7da; border-radius: 5px; border: 1px solid #f5c6cb;">
                        <h4>❌ 파일 목록 로드 실패</h4>
                        <p>오류: ${error.message}</p>
                        <button onclick="loadFileList()" class="upload-btn" style="margin-top: 10px;">다시 시도</button>
                    </div>
                `;
            }
        }

        // 파일별 데이터 삭제
        async function deleteFileData(filename) {
            const confirmMessage = `⚠️ 엑셀 파일 삭제 확인\n\n` +
                                 `파일명: ${filename}\n\n` +
                                 `이 엑셀 파일의 모든 주문 데이터가 삭제됩니다.\n` +
                                 `삭제된 데이터는 복구할 수 없습니다.\n\n` +
                                 `정말 삭제하시겠습니까?`;

            if (!confirm(confirmMessage)) {
                return;
            }

            try {
                const response = await fetch('upload_orderform_excel.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: `action=delete_file&filename=${encodeURIComponent(filename)}`
                });

                const result = await response.text();
                document.getElementById('result').innerHTML = result;
                
                // 삭제 후 파일 목록 새로고침
                setTimeout(() => {
                    loadFileList();
                }, 1000);
                
            } catch (error) {
                alert('삭제 중 오류가 발생했습니다: ' + error.message);
            }
        }

        // 페이지 로드 시 파일 목록 로드
        window.addEventListener('load', () => {
            console.log('페이지 로드 완료, 파일 목록 로드 시작...');
            loadFileList();
        });

        // DOM이 준비되면 즉시 로드
        document.addEventListener('DOMContentLoaded', () => {
            console.log('DOM 준비 완료, 파일 목록 로드 시작...');
            setTimeout(() => {
                loadFileList();
            }, 500); // 0.5초 후 로드
        });
    </script>
</body>
</html>

<?php
// PHP 처리 부분
use PhpOffice\PhpSpreadsheet\IOFactory;

// 파일 목록 조회 요청 처리
if (isset($_GET['action']) && $_GET['action'] === 'list_files') {
    // 출력 버퍼 정리 (HTML 출력 방지)
    ob_clean();

    require_once '../conf/Database.php';

    try {
        $db = new Database();

        // 먼저 orderForm 테이블에 새 컬럼들이 있는지 확인
        $columns = $db->select("SHOW COLUMNS FROM orderForm");
        $hasMapping = false;
        foreach ($columns as $column) {
            if ($column['Field'] === 'mapping_status') {
                $hasMapping = true;
                break;
            }
        }

        if ($hasMapping) {
            // 새 컬럼이 있는 경우
            $sql = "SELECT
                        filename,
                        COUNT(*) as count,
                        DATE_FORMAT(MIN(date_join), '%Y-%m-%d %H:%i:%s') as date_join,
                        SUM(order_amount) as total_amount,
                        COUNT(CASE WHEN mapping_status = 'mapped' THEN 1 END) as mapped_count,
                        COUNT(CASE WHEN mapping_status = 'unmapped' THEN 1 END) as unmapped_count
                    FROM orderForm
                    GROUP BY filename
                    ORDER BY MIN(date_join) DESC";
        } else {
            // 기존 컬럼만 있는 경우 (호환성)
            $sql = "SELECT
                        filename,
                        COUNT(*) as count,
                        DATE_FORMAT(MIN(date_join), '%Y-%m-%d %H:%i:%s') as date_join,
                        SUM(order_amount) as total_amount,
                        0 as mapped_count,
                        COUNT(*) as unmapped_count
                    FROM orderForm
                    GROUP BY filename
                    ORDER BY MIN(date_join) DESC";
        }

        $files = $db->select($sql);

        header('Content-Type: application/json');
        echo json_encode([
            'files' => $files,
            'hasMapping' => $hasMapping,
            'debug' => [
                'sql' => $sql,
                'fileCount' => count($files)
            ]
        ]);
        exit;

    } catch (Exception $e) {
        // 출력 버퍼 정리
        ob_clean();

        header('Content-Type: application/json');
        http_response_code(500);
        echo json_encode([
            'error' => $e->getMessage(),
            'debug' => [
                'action' => 'list_files',
                'file' => basename(__FILE__),
                'line' => __LINE__,
                'trace' => $e->getTraceAsString()
            ]
        ]);
        exit;
    }
}

// 파일별 데이터 삭제 요청 처리
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'delete_file') {
    // 출력 버퍼 정리
    ob_clean();

    require_once '../conf/Database.php';

    try {
        $db = new Database();
        $filename = $_POST['filename'];

        // 정확한 파일명으로 데이터 삭제
        $deletedRows = $db->delete('orderForm', 'filename = :filename', ['filename' => $filename]);

        echo '<div class="result success">';
        echo '<h3>🗑️ 파일 데이터 삭제 완료</h3>';
        echo "<p>파일명: " . htmlspecialchars($filename) . "</p>";
        echo "<p>삭제된 항목 수: {$deletedRows}개</p>";
        echo '</div>';
        exit;

    } catch (Exception $e) {
        echo '<div class="result error">';
        echo '<h3>❌ 삭제 실패</h3>';
        echo '<p>' . htmlspecialchars($e->getMessage()) . '</p>';
        echo '</div>';
        exit;
    }
}
/*

*/
// 엑셀 파일 업로드 처리
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['excel_file'])) {
    require_once '../vendor/autoload.php';
    require_once '../conf/Database.php';

    try {
        // 데이터베이스 연결
        $db = new Database();

        // orderForm 테이블 생성 (존재하지 않는 경우)
        $createTableSql = "
            CREATE TABLE IF NOT EXISTS orderForm (
                id INT AUTO_INCREMENT PRIMARY KEY,
                product_name_sur VARCHAR(255) NOT NULL,
                product_name_option VARCHAR(255) NOT NULL,
                order_amount INT NOT NULL,
                date_join DATETIME NOT NULL,
                filename VARCHAR(255) NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ";

        $db->connect()->exec($createTableSql);

        // 업로드된 파일 처리
        $uploadedFile = $_FILES['excel_file']['tmp_name'];
        $originalFileName = $_FILES['excel_file']['name'];

        if (!file_exists($uploadedFile)) {
            throw new Exception('파일 업로드에 실패했습니다.');
        }

        // 파일명에서 날짜/시간 추출 (언더스코어 이후 부분만 검사)
        $dateJoin = null;

        // 디버깅: 파일명 출력
        echo '<p>디버깅 - 업로드된 파일명: ' . htmlspecialchars($originalFileName) . '</p>';

        // 파일명에서 확장자 제거
        $fileNameWithoutExt = pathinfo($originalFileName, PATHINFO_FILENAME);
        echo '<p>디버깅 - 확장자 제거된 파일명: ' . htmlspecialchars($fileNameWithoutExt) . '</p>';

        // 마지막 언더스코어(_) 위치 찾기
        $lastUnderscorePos = strrpos($fileNameWithoutExt, '_');

        if ($lastUnderscorePos !== false) {
            // 언더스코어 이후 부분 추출
            $dateTimeStr = substr($fileNameWithoutExt, $lastUnderscorePos + 1);
            echo '<p>디버깅 - 언더스코어 이후 추출된 문자열: ' . htmlspecialchars($dateTimeStr) . '</p>';
            echo '<p>디버깅 - 추출된 문자열 길이: ' . strlen($dateTimeStr) . '</p>';

            // 14자리 숫자인지 확인
            if (strlen($dateTimeStr) === 14 && ctype_digit($dateTimeStr)) {
                $year = substr($dateTimeStr, 0, 4);
                $month = substr($dateTimeStr, 4, 2);
                $day = substr($dateTimeStr, 6, 2);
                $hour = substr($dateTimeStr, 8, 2);
                $minute = substr($dateTimeStr, 10, 2);
                $second = substr($dateTimeStr, 12, 2);

                $dateJoin = "{$year}-{$month}-{$day} {$hour}:{$minute}:{$second}";
                echo '<p>디버깅 - 파싱 성공! 추출된 날짜: ' . $dateJoin . '</p>';
            } else {
                throw new Exception('파일명의 날짜/시간 형식이 올바르지 않습니다. 언더스코어(_) 이후에 14자리 숫자가 와야 합니다. (현재: ' . $dateTimeStr . ')');
            }
        } else {
            throw new Exception('파일명에 언더스코어(_)가 없습니다. 파일명 형식: "파일명_YYYYMMDDHHMMSS.xlsx"');
        }

        // 텍스트 치환 함수
        function replaceSpecialChars($text) {
            $text = str_replace('🕔', '이', $text);
            $text = str_replace('잠❹', '잠옷', $text);
            $text = str_replace('로❹', '로우', $text);
            $text = str_replace('벽❹', '벽옷', $text);
            $text = str_replace('도❹', '도우', $text);
            return $text;
        }

        // 엑셀 파일 읽기
        $spreadsheet = IOFactory::load($uploadedFile);
        $worksheet = $spreadsheet->getActiveSheet();
        $highestRow = $worksheet->getHighestRow();

        echo '<div class="result success">';
        echo '<h3>📊 주문서 엑셀 파일 처리 결과</h3>';
        echo '<p>파일명: ' . htmlspecialchars($originalFileName) . '</p>';
        echo '<p>추출된 날짜: ' . $dateJoin . '</p>';
        echo '<p>총 ' . ($highestRow - 2) . '개의 데이터를 처리합니다.</p>';

        $successCount = 0;
        $errorCount = 0;
        $errors = [];

        // 트랜잭션 시작
        $db->beginTransaction();

        // 3행부터 데이터 처리 (1행은 빈 행, 2행은 헤더)
        for ($row = 3; $row <= $highestRow; $row++) {
            try {
                $productNameSur = $worksheet->getCell('B' . $row)->getValue();
                $productNameOption = $worksheet->getCell('C' . $row)->getValue();
                $orderAmount = $worksheet->getCell('D' . $row)->getValue();

                // 빈 행 건너뛰기
                if (empty($productNameSur) && empty($productNameOption) && empty($orderAmount)) {
                    continue;
                }

                // 특수 문자 치환
                $productNameSur = replaceSpecialChars($productNameSur);
                $productNameOption = replaceSpecialChars($productNameOption);

                // 수량이 숫자가 아닌 경우 0으로 처리
                $orderAmount = is_numeric($orderAmount) ? intval($orderAmount) : 0;

                // 기존 매핑 확인
                $mappingCheckSql = "SELECT option_no FROM product_mapping
                                   WHERE product_name_sur = :product_name_sur
                                   AND product_name_option = :product_name_option";

                $mappingResult = $db->selectOne($mappingCheckSql, [
                    'product_name_sur' => $productNameSur,
                    'product_name_option' => $productNameOption
                ]);

                $mappingStatus = 'unmapped';
                $mappedOptionNo = null;

                if ($mappingResult) {
                    $mappingStatus = 'mapped';
                    $mappedOptionNo = $mappingResult['option_no'];
                }

                // 데이터 삽입
                $data = [
                    'product_name_sur' => $productNameSur,
                    'product_name_option' => $productNameOption,
                    'order_amount' => $orderAmount,
                    'date_join' => $dateJoin,
                    'filename' => $originalFileName,
                    'mapping_status' => $mappingStatus,
                    'mapped_option_no' => $mappedOptionNo
                ];

                $insertId = $db->insert('orderForm', $data);
                $successCount++;

                $mappingIcon = ($mappingStatus === 'mapped') ? '🔗' : '❓';
                $mappingText = ($mappingStatus === 'mapped') ? "연결됨({$mappedOptionNo})" : '미연결';

                echo '<p>' . $mappingIcon . ' 행 ' . $row . ': ' . htmlspecialchars($productNameSur) . ' - ' . htmlspecialchars($productNameOption) . ' (수량: ' . $orderAmount . ', ' . $mappingText . ')</p>';

            } catch (Exception $e) {
                $errorCount++;
                $errors[] = "행 {$row}: " . $e->getMessage();
                echo '<p>❌ 행 ' . $row . ': 오류 - ' . htmlspecialchars($e->getMessage()) . '</p>';
            }
        }

        // 트랜잭션 커밋
        $db->commit();

        // 매핑 상태 요약
        $mappedCountSql = "SELECT COUNT(*) as count FROM orderForm
                          WHERE filename = :filename AND mapping_status = 'mapped'";
        $mappedCount = $db->selectOne($mappedCountSql, ['filename' => $originalFileName])['count'];
        $unmappedCount = $successCount - $mappedCount;

        echo '<hr>';
        echo '<h4>📈 처리 완료</h4>';
        echo '<p>성공: ' . $successCount . '개</p>';
        echo '<p>실패: ' . $errorCount . '개</p>';
        echo '<p>🔗 연결된 상품: ' . $mappedCount . '개</p>';
        echo '<p>❓ 미연결 상품: ' . $unmappedCount . '개</p>';

        if ($unmappedCount > 0) {
            echo '<div style="background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin-top: 15px;">';
            echo '<h4 style="color: #856404; margin-top: 0;">⚠️ 미연결 상품이 있습니다</h4>';
            echo '<p style="color: #856404;">미연결된 ' . $unmappedCount . '개 상품이 있습니다. 재고 계산을 위해 상품 매핑이 필요합니다.</p>';
            echo '<a href="compare_product_stock.php" style="display: inline-block; padding: 10px 20px; background-color: #007cba; color: white; text-decoration: none; border-radius: 5px;">상품 연결 관리 페이지로 이동</a>';
            echo '</div>';
        }

        if (!empty($errors)) {
            echo '<h4>❌ 오류 목록</h4>';
            foreach ($errors as $error) {
                echo '<p style="color: red;">' . htmlspecialchars($error) . '</p>';
            }
        }

        echo '</div>';

    } catch (Exception $e) {
        // 트랜잭션 롤백 (트랜잭션이 시작된 경우에만)
        if (isset($db)) {
            try {
                $db->rollback();
            } catch (Exception $rollbackException) {
                // 트랜잭션이 시작되지 않았거나 이미 종료된 경우 무시
            }
        }

        echo '<div class="result error">';
        echo '<h3>❌ 처리 실패</h3>';
        echo '<p>' . htmlspecialchars($e->getMessage()) . '</p>';
        echo '</div>';
    }
}
?>
