<?php
/**
 * orderForm 테이블 디버깅 페이지
 * 업로드된 데이터를 확인하기 위한 디버깅 도구
 */

require_once '../conf/Database.php';

echo "<h1>📊 orderForm 테이블 디버깅</h1>";

try {
    $db = new Database();
    
    // 1. 테이블 구조 확인
    echo "<h2>1. 테이블 구조</h2>";
    $columns = $db->select("SHOW COLUMNS FROM orderForm");
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>컬럼명</th><th>타입</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($column['Field']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Type']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Null']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Key']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Default']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Extra']) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // 2. 전체 데이터 수 확인
    echo "<h2>2. 전체 데이터 수</h2>";
    $totalCount = $db->selectOne("SELECT COUNT(*) as count FROM orderForm")['count'];
    echo "<p><strong>총 {$totalCount}개의 주문 데이터가 있습니다.</strong></p>";
    
    if ($totalCount == 0) {
        echo "<div style='color: red; padding: 20px; background-color: #ffe6e6; border-radius: 5px;'>";
        echo "<h3>⚠️ 데이터가 없습니다!</h3>";
        echo "<p>orderForm 테이블에 데이터가 없습니다. 엑셀 파일을 업로드해주세요.</p>";
        echo "</div>";
        exit;
    }
    
    // 3. 파일별 통계
    echo "<h2>3. 파일별 통계</h2>";
    $fileStats = $db->select("
        SELECT 
            filename,
            COUNT(*) as count,
            MIN(date_join) as first_upload,
            MAX(date_join) as last_upload,
            SUM(order_amount) as total_amount
        FROM orderForm 
        GROUP BY filename 
        ORDER BY MIN(date_join) DESC
    ");
    
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>파일명</th><th>항목수</th><th>총 주문량</th><th>첫 업로드</th><th>마지막 업로드</th></tr>";
    foreach ($fileStats as $stat) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($stat['filename']) . "</td>";
        echo "<td>" . number_format($stat['count']) . "</td>";
        echo "<td>" . number_format($stat['total_amount']) . "</td>";
        echo "<td>" . $stat['first_upload'] . "</td>";
        echo "<td>" . $stat['last_upload'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // 4. 매핑 상태 확인 (컬럼이 있는 경우)
    $hasMapping = false;
    foreach ($columns as $column) {
        if ($column['Field'] === 'mapping_status') {
            $hasMapping = true;
            break;
        }
    }
    
    if ($hasMapping) {
        echo "<h2>4. 매핑 상태</h2>";
        $mappingStats = $db->select("
            SELECT 
                mapping_status,
                COUNT(*) as count
            FROM orderForm 
            GROUP BY mapping_status
        ");
        
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>매핑 상태</th><th>개수</th></tr>";
        foreach ($mappingStats as $stat) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($stat['mapping_status']) . "</td>";
            echo "<td>" . number_format($stat['count']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<h2>4. 매핑 상태</h2>";
        echo "<p style='color: orange;'>⚠️ mapping_status 컬럼이 없습니다. 데이터베이스 설정을 실행해주세요.</p>";
        echo "<a href='setup_database_tables.php' style='padding: 10px 20px; background-color: #007cba; color: white; text-decoration: none; border-radius: 5px;'>데이터베이스 설정 실행</a>";
    }
    
    // 5. 최근 데이터 샘플
    echo "<h2>5. 최근 데이터 샘플 (최대 10개)</h2>";
    $sampleData = $db->select("SELECT * FROM orderForm ORDER BY id DESC LIMIT 10");
    
    if (!empty($sampleData)) {
        echo "<table border='1' cellpadding='5' style='width: 100%; font-size: 12px;'>";
        echo "<tr>";
        foreach (array_keys($sampleData[0]) as $key) {
            echo "<th>" . htmlspecialchars($key) . "</th>";
        }
        echo "</tr>";
        
        foreach ($sampleData as $row) {
            echo "<tr>";
            foreach ($row as $value) {
                echo "<td>" . htmlspecialchars($value) . "</td>";
            }
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // 6. 파일 목록 API 테스트
    echo "<h2>6. 파일 목록 API 테스트</h2>";
    
    if ($hasMapping) {
        $apiSql = "SELECT 
                    filename,
                    COUNT(*) as count,
                    DATE_FORMAT(MIN(date_join), '%Y-%m-%d %H:%i:%s') as date_join,
                    SUM(order_amount) as total_amount,
                    COUNT(CASE WHEN mapping_status = 'mapped' THEN 1 END) as mapped_count,
                    COUNT(CASE WHEN mapping_status = 'unmapped' THEN 1 END) as unmapped_count
                FROM orderForm 
                GROUP BY filename 
                ORDER BY MIN(date_join) DESC";
    } else {
        $apiSql = "SELECT 
                    filename,
                    COUNT(*) as count,
                    DATE_FORMAT(MIN(date_join), '%Y-%m-%d %H:%i:%s') as date_join,
                    SUM(order_amount) as total_amount,
                    0 as mapped_count,
                    COUNT(*) as unmapped_count
                FROM orderForm 
                GROUP BY filename 
                ORDER BY MIN(date_join) DESC";
    }
    
    echo "<p><strong>실행할 SQL:</strong></p>";
    echo "<pre style='background-color: #f8f9fa; padding: 10px; border-radius: 5px;'>" . htmlspecialchars($apiSql) . "</pre>";
    
    $apiResult = $db->select($apiSql);
    echo "<p><strong>결과:</strong></p>";
    echo "<pre style='background-color: #f8f9fa; padding: 10px; border-radius: 5px;'>" . htmlspecialchars(json_encode($apiResult, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)) . "</pre>";
    
} catch (Exception $e) {
    echo "<div style='color: red; padding: 20px; background-color: #ffe6e6; border: 1px solid #ff9999; border-radius: 5px;'>";
    echo "<h3>❌ 오류 발생</h3>";
    echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    background-color: #f5f5f5;
}
table {
    background: white;
    border-collapse: collapse;
    width: 100%;
    margin-bottom: 20px;
}
th, td {
    padding: 8px;
    text-align: left;
    border: 1px solid #ddd;
}
th {
    background-color: #007cba;
    color: white;
}
h1, h2 {
    color: #333;
}
pre {
    overflow-x: auto;
    white-space: pre-wrap;
}
</style>
