<!DOCTYPE html>
<html lang="ko">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>재입고 테이블 생성 - 재고관리시스템</title>
</head>
<body>
    <?php include '../components/navbar.php'; ?>
    
    <div class="page-content" style="max-width: 1000px; margin: 20px auto; padding: 20px; background: white; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">

<?php
/**
 * 재입고 테이블 생성 스크립트
 */

require_once '../conf/Database.php';

try {
    $db = new Database();
    
    echo "<h2>📦 재입고 테이블 생성</h2>";
    
    // 재입고 테이블 생성
    $createRestockTableSql = "
        CREATE TABLE IF NOT EXISTS product_restock (
            id INT AUTO_INCREMENT PRIMARY KEY,
            option_no VARCHAR(100) NOT NULL COMMENT '상품 옵션번호',
            restock_amount INT NOT NULL COMMENT '재입고 수량 (양수: 입고, 음수: 출고)',
            restock_reason VARCHAR(255) DEFAULT NULL COMMENT '재입고 사유',
            restock_date DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '재입고 일시',
            created_by VARCHAR(100) DEFAULT 'system' COMMENT '작업자',
            notes TEXT DEFAULT NULL COMMENT '비고',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '생성일시',
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '수정일시',
            INDEX idx_option_no (option_no),
            INDEX idx_restock_date (restock_date),
            FOREIGN KEY (option_no) REFERENCES product_master(option_no) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='상품 재입고 이력 테이블'
    ";
    
    $db->connect()->exec($createRestockTableSql);
    echo "✅ product_restock 테이블 생성 완료<br>";
    
    // 테이블 구조 확인
    echo "<h3>📋 product_restock 테이블 구조</h3>";
    $columns = $db->select("DESCRIBE product_restock");
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>컬럼명</th><th>타입</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($column['Field'] ?? '') . "</td>";
        echo "<td>" . htmlspecialchars($column['Type'] ?? '') . "</td>";
        echo "<td>" . htmlspecialchars($column['Null'] ?? '') . "</td>";
        echo "<td>" . htmlspecialchars($column['Key'] ?? '') . "</td>";
        echo "<td>" . htmlspecialchars($column['Default'] ?? '') . "</td>";
        echo "<td>" . htmlspecialchars($column['Extra'] ?? '') . "</td>";
        echo "</tr>";
    }
    echo "</table><br>";
    
    // 샘플 데이터 확인
    echo "<h3>📊 현재 데이터 현황</h3>";
    $restockCount = $db->selectOne("SELECT COUNT(*) as count FROM product_restock")['count'];
    echo "<p>재입고 이력: {$restockCount}개</p>";
    
    $productCount = $db->selectOne("SELECT COUNT(*) as count FROM product_master")['count'];
    echo "<p>상품 마스터: {$productCount}개</p>";
    
    echo "<h3>🎉 재입고 테이블 설정 완료!</h3>";
    echo "<p>이제 재입고 기능을 사용할 수 있습니다.</p>";
    
    echo '<div style="text-align: center; margin-top: 20px;">';
    echo '<a href="../page/view_product_master.php" style="padding: 10px 20px; background-color: #007cba; color: white; text-decoration: none; border-radius: 5px; margin: 5px;">📦 재고 확인 페이지로 이동</a>';
    echo '<a href="../page/manage_product.php" style="padding: 10px 20px; background-color: #28a745; color: white; text-decoration: none; border-radius: 5px; margin: 5px;">✏️ 상품 관리 페이지로 이동</a>';
    echo '</div>';
    
} catch (Exception $e) {
    echo "<div style='color: red; padding: 20px; background-color: #ffe6e6; border: 1px solid #ff9999; border-radius: 5px;'>";
    echo "<h3>❌ 오류 발생</h3>";
    echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}
?>

<style>
body {
    font-family: Arial, sans-serif;
    background-color: #f5f5f5;
}
table {
    background: white;
    border-collapse: collapse;
    width: 100%;
    margin-bottom: 20px;
}
th, td {
    padding: 8px;
    text-align: left;
    border: 1px solid #ddd;
}
th {
    background-color: #007cba;
    color: white;
}
h2, h3 {
    color: #333;
}
</style>

    </div>
</body>
</html>
