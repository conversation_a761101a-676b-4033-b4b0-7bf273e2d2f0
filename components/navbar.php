<?php
/**
 * 네비게이션 메뉴바 컴포넌트
 * 모든 페이지에서 공통으로 사용되는 상단 메뉴바
 */

// 현재 페이지 경로를 기준으로 상대 경로 계산
function getBasePath() {
    $currentPath = $_SERVER['REQUEST_URI'];
    $pathParts = explode('/', trim($currentPath, '/'));
    
    // 현재 위치에 따라 기본 경로 설정
    if (strpos($currentPath, '/page/') !== false) {
        return '../';  // page 폴더에서 실행
    } elseif (strpos($currentPath, '/util/') !== false) {
        return '../';  // util 폴더에서 실행
    } else {
        return './';   // 루트에서 실행
    }
}

$basePath = getBasePath();

// 현재 페이지 확인 (활성 메뉴 표시용)
$currentPage = basename($_SERVER['PHP_SELF']);
?>

<style>
/* 네비게이션 바 스타일 */
.navbar {
    background: linear-gradient(135deg, #007cba 0%, #005a8b 100%);
    padding: 0;
    margin: 0;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    position: sticky;
    top: 0;
    z-index: 1000;
    font-family: Arial, sans-serif;
}

.navbar-container {
    max-width: 1400px;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
}

.navbar-brand {
    color: white;
    font-size: 24px;
    font-weight: bold;
    text-decoration: none;
    padding: 15px 0;
    display: flex;
    align-items: center;
}

.navbar-brand:hover {
    color: #e3f2fd;
    text-decoration: none;
}

.navbar-brand .logo {
    margin-right: 10px;
    font-size: 28px;
}

.navbar-menu {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
    align-items: center;
}

.navbar-item {
    position: relative;
}

.navbar-link {
    color: white;
    text-decoration: none;
    padding: 15px 20px;
    display: block;
    transition: all 0.3s ease;
    border-radius: 5px;
    margin: 0 2px;
    font-weight: 500;
}

.navbar-link:hover {
    background-color: rgba(255,255,255,0.1);
    color: #e3f2fd;
    text-decoration: none;
    transform: translateY(-1px);
}

.navbar-link.active {
    background-color: rgba(255,255,255,0.2);
    color: #ffffff;
    font-weight: bold;
}

/* 드롭다운 메뉴 */
.dropdown {
    position: relative;
}

.dropdown-content {
    display: none;
    position: absolute;
    top: 100%;
    right: 0;
    background-color: white;
    min-width: 250px;
    box-shadow: 0 8px 16px rgba(0,0,0,0.2);
    border-radius: 5px;
    overflow: hidden;
    z-index: 1001;
}

.dropdown:hover .dropdown-content {
    display: block;
}

.dropdown-item {
    color: #333;
    padding: 12px 20px;
    text-decoration: none;
    display: block;
    transition: background-color 0.3s ease;
    border-bottom: 1px solid #f0f0f0;
}

.dropdown-item:last-child {
    border-bottom: none;
}

.dropdown-item:hover {
    background-color: #f8f9fa;
    color: #007cba;
    text-decoration: none;
}

.dropdown-item .icon {
    margin-right: 10px;
    width: 20px;
    display: inline-block;
}

/* 모바일 반응형 */
@media (max-width: 768px) {
    .navbar-container {
        flex-direction: column;
        padding: 10px;
    }
    
    .navbar-brand {
        margin-bottom: 10px;
    }
    
    .navbar-menu {
        flex-wrap: wrap;
        justify-content: center;
    }
    
    .navbar-link {
        padding: 10px 15px;
        font-size: 14px;
    }
    
    .dropdown-content {
        position: static;
        display: block;
        box-shadow: none;
        background-color: rgba(255,255,255,0.1);
        margin-top: 5px;
    }
    
    .dropdown-item {
        color: white;
        border-bottom: 1px solid rgba(255,255,255,0.1);
    }
    
    .dropdown-item:hover {
        background-color: rgba(255,255,255,0.1);
        color: white;
    }
}

/* 페이지 컨텐츠와의 간격 */
.page-content {
    margin-top: 0;
}

/* 알림 배지 */
.badge {
    background-color: #dc3545;
    color: white;
    border-radius: 50%;
    padding: 2px 6px;
    font-size: 11px;
    margin-left: 5px;
    font-weight: bold;
}
</style>

<nav class="navbar">
    <div class="navbar-container">
        <!-- 브랜드/로고 -->
        <a href="<?php echo $basePath; ?>page/view_product_master.php" class="navbar-brand">
            <span class="logo">📦</span>
            재고관리시스템
        </a>
        
        <!-- 메뉴 항목들 -->
        <ul class="navbar-menu">
            <!-- 홈 (재고 확인) -->
            <li class="navbar-item">
                <a href="<?php echo $basePath; ?>page/view_product_master.php" 
                   class="navbar-link <?php echo ($currentPage == 'view_product_master.php') ? 'active' : ''; ?>">
                    🏠 홈
                </a>
            </li>
            
            <!-- 주문서 업로드 -->
            <li class="navbar-item">
                <a href="<?php echo $basePath; ?>page/upload_orderform_excel.php" 
                   class="navbar-link <?php echo ($currentPage == 'upload_orderform_excel.php') ? 'active' : ''; ?>">
                    📤 주문서 업로드
                </a>
            </li>
            
            <!-- 상품 연결 -->
            <li class="navbar-item">
                <a href="<?php echo $basePath; ?>page/compare_product_stock.php" 
                   class="navbar-link <?php echo ($currentPage == 'compare_product_stock.php') ? 'active' : ''; ?>">
                    🔗 상품 연결
                    <?php
                    // 미연결 상품 수 표시 (선택적)
                    try {
                        require_once dirname(__FILE__) . '/../conf/Database.php';
                        $db = new Database();
                        $unmappedCount = $db->selectOne("SELECT COUNT(*) as count FROM orderForm WHERE mapping_status = 'unmapped'")['count'];
                        if ($unmappedCount > 0) {
                            echo '<span class="badge">' . $unmappedCount . '</span>';
                        }
                    } catch (Exception $e) {
                        // 오류 시 무시
                    }
                    ?>
                </a>
            </li>
            
            <!-- 재고 확인 -->
            <li class="navbar-item">
                <a href="<?php echo $basePath; ?>page/view_product_master.php" 
                   class="navbar-link <?php echo ($currentPage == 'view_product_master.php') ? 'active' : ''; ?>">
                    📊 재고 확인
                </a>
            </li>
            
            <!-- 개발자 도구 (드롭다운) -->
            <li class="navbar-item dropdown">
                <a href="#" class="navbar-link">
                    🛠️ 개발자 도구 ▼
                </a>
                <div class="dropdown-content">
                    <a href="<?php echo $basePath; ?>util/setup_database_tables.php" class="dropdown-item">
                        <span class="icon">🗄️</span>
                        데이터베이스 설정
                    </a>
                    <a href="<?php echo $basePath; ?>util/init_excel_product_master.php" class="dropdown-item">
                        <span class="icon">📦</span>
                        상품 마스터 등록
                    </a>
                    <a href="<?php echo $basePath; ?>page/manage_product.php" class="dropdown-item">
                        <span class="icon">✏️</span>
                        상품 관리
                    </a>
                    <a href="<?php echo $basePath; ?>util/reset_data.php" class="dropdown-item" 
                       onclick="return confirm('정말로 데이터 리셋 페이지로 이동하시겠습니까?')">
                        <span class="icon">🗑️</span>
                        데이터 리셋
                    </a>
                </div>
            </li>
        </ul>
    </div>
</nav>

<script>
// 드롭다운 메뉴 모바일 지원
document.addEventListener('DOMContentLoaded', function() {
    const dropdowns = document.querySelectorAll('.dropdown');
    
    dropdowns.forEach(dropdown => {
        const link = dropdown.querySelector('.navbar-link');
        const content = dropdown.querySelector('.dropdown-content');
        
        // 모바일에서 클릭으로 드롭다운 토글
        if (window.innerWidth <= 768) {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                content.style.display = content.style.display === 'block' ? 'none' : 'block';
            });
        }
    });
    
    // 외부 클릭 시 드롭다운 닫기
    document.addEventListener('click', function(e) {
        if (!e.target.closest('.dropdown')) {
            dropdowns.forEach(dropdown => {
                const content = dropdown.querySelector('.dropdown-content');
                if (window.innerWidth <= 768) {
                    content.style.display = 'none';
                }
            });
        }
    });
});

// 현재 페이지 하이라이트
document.addEventListener('DOMContentLoaded', function() {
    const currentPath = window.location.pathname;
    const navLinks = document.querySelectorAll('.navbar-link');
    
    navLinks.forEach(link => {
        const linkPath = new URL(link.href).pathname;
        if (currentPath.includes(linkPath.split('/').pop())) {
            link.classList.add('active');
        }
    });
});
</script>
